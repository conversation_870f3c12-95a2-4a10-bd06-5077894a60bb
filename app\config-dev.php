<?php
// config-dev.php

return [
	"appEnvironment" => "dev",
	"baseUrl" => "http://localhost",
	"meilisearchApiKey" => "sLaNqv8Z572ee7fec116907d216d91069c617501f4acbd4186df78a760540682f9eb622f",

	"tnbcNewAgentPortalRequest" =>
	[
		"toEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott <PERSON> - HitHome Solutions LLC (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "<PERSON> (dev)"
			]
		],
		"ccEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott <PERSON> - The Life Stage Brokerage Network (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "<PERSON> (dev)"
			]
		]
	],

	"tnbcCaseConsultationRequest" =>
	[
		"toEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - HitHome Solutions LLC (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		],
		"ccEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - The Life Stage Brokerage Network (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		]
	],


	"apasiNewAgentContractRequest" =>
	[
		"toEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - HitHome Solutions LLC (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		],
		"ccEmailAddresses" =>
		[]
	],

	"blpNewAgentContractRequest" =>
	[
		"toEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - HitHome Solutions LLC (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		],
		"ccEmailAddresses" =>
		[]
	],

	"mikeReiselConversationRequest" =>
	[
		"toEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - HitHome Solutions LLC (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		],
		"ccEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - The Life Stage Brokerage Network (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		]
	],

	"lifeCaseConsultationRequest" =>
	[
		"toEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - HitHome Solutions LLC (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		],
		"ccEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - The Life Stage Brokerage Network (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		]
	],

	"lifeCaseJointConsultationRequest" =>
	[
		"toEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - HitHome Solutions LLC (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		],
		"ccEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - The Life Stage Brokerage Network (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		]
	],

	"annuityCaseConsultationRequest" =>
	[
		"toEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - HitHome Solutions LLC (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		],
		"ccEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - The Life Stage Brokerage Network (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		]
	],

	"annuityCaseJointConsultationRequest" =>
	[
		"toEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - HitHome Solutions LLC (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		],
		"ccEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - The Life Stage Brokerage Network (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		]
	],

	"disabilityCaseConsultationRequest" =>
	[
		"toEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - HitHome Solutions LLC (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		],
		"ccEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - The Life Stage Brokerage Network (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		]
	],

	"disabilityCaseJointConsultationRequest" =>
	[
		"toEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - HitHome Solutions LLC (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		],
		"ccEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - The Life Stage Brokerage Network (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		]
	],

	"medMutalProtectCaseConsultationRequest" =>
	[
		"toEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - HitHome Solutions LLC (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev) medmutual protect"
			]
		],
		"ccEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - The Life Stage Brokerage Network (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		]
	],

	"gwicCaseConsultationRequest" =>
	[
		"toEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - HitHome Solutions LLC (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev) medmutual protect"
			]
		],
		"ccEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - The Life Stage Brokerage Network (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		]
	],


	"ltcCaseConsultationRequest" =>
	[
		"toEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - HitHome Solutions LLC (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		],
		"ccEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - The Life Stage Brokerage Network (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		]
	],

	"ltcCaseJointConsultationRequest" =>
	[
		"toEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - HitHome Solutions LLC (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		],
		"ccEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - The Life Stage Brokerage Network (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		]
	],

	"medicareCaseConsultationRequest" =>
	[
		"toEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - HitHome Solutions LLC (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		],
		"ccEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - The Life Stage Brokerage Network (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		]
	],

	"champHealthCaseConsultationRequest" =>
	[
		"toEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - HitHome Solutions LLC (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		],
		"ccEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - The Life Stage Brokerage Network (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		]
	],

	"groupBenefitsBusinessConsultationRequest" =>
	[
		"toEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - HitHome Solutions LLC (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		],
		"ccEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - The Life Stage Brokerage Network (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		]
	],

	"metalsCaseConsultationRequest" =>
	[
		"toEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - HitHome Solutions LLC (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		],
		"ccEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - The Life Stage Brokerage Network (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		]
	],

	"informationRequest" =>
	[
		"toEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - HitHome Solutions LLC (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		],
		"ccEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - The Life Stage Brokerage Network (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		]
	],

	"findOutMoreRequest" =>
	[
		"toEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - HitHome Solutions LLC (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		],
		"ccEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - The Life Stage Brokerage Network (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		]
	],

	"lifeStageAdvisorConsultationRequest" =>
	[
		"toEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - HitHome Solutions LLC (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		],
		"ccEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - The Life Stage Brokerage Network (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		]
	],

	"primaryContacts" =>
	[
		"Scott Weston" =>
		[
			"emailAddress" => "<EMAIL>",
			"emailName" => "Scott Weston - The Life Stage Brokerage Network (dev)"
		]
	],

	"rsvp2022CollegeBasketballViewingEvent" =>
	[
		"toEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - HitHome Solutions LLC (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		],
		"ccEmailAddresses" =>
		[
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston - The Life Stage Brokerage Network (dev)"
			],
			[
				"address" => "<EMAIL>",
				"name" => "Scott Weston (dev)"
			]
		]
	]
];
