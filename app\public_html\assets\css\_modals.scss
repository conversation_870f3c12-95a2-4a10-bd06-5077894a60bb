/* =============================================================*/
/* Modals.....  */
/**/
/**/
.information-request-modal,
.tnbc-case-consultation-request-modal,
.mike-reisel-conversation-request-modal,
.find-out-more-modal,
.fi-modal,
.webinar-gold-silver-modal,
.webinar-gold-silver-introduction-modal,
.ryans-story-modal,
.life-case-consultation-request-modal,
.life-case-joint-consultation-request-modal,
.annuity-case-consultation-request-modal,
.annuity-case-joint-consultation-request-modal,
.annuity-myga-rates-modal,
.disability-case-consultation-request-modal,
.disability-case-joint-consultation-request-modal,
.ltc-case-consultation-request-modal,
.ltc-case-joint-consultation-request-modal,
.medicare-case-consultation-request-modal,
.champ-health-case-consultation-request-modal,
.group-benefits-business-consultation-request-modal,
.medmutualprotect-case-consultation-request-modal,
.gwic-case-consultation-request-modal,
.metals-case-consultation-request-modal,
.category-modal,
.service-modal,
.insco-modal,
.modal {
	position: fixed;
	z-index: 100;
	top: 0;
	bottom: var(--hght-footer);
	left: 1em;
	right: 1em;
	margin: 1em auto;
	margin-bottom: 0.25em;
	min-width: 165px;
	max-width: 1100px;
	box-shadow: 0 0 0 100vw rgba(black, 0.9);
	pointer-events: auto;
	border-radius: 0.2em;
	background: #000;

	p {
		font-size: 1.25rem;
	}

	&.is-hidden {
		display: none;
	}

	&:target {
		// Used to bring up modal directly from address bar for seo.
		transform: scale(1);
		z-index: 101; // Need to add z-index that is different than element in order to work around a known bug in google chrome that does not repaint an element. (For this application it is necessary only when javascript is disabled and only anchor is used to bring up the modal.) REFERENCE: http://thenewcode.com/1024/Forcing-a-Webkit-Repaint
	}

	&__close-button {
		position: absolute;
		z-index: 2;
		top: -0.45em;
		left: -0.45em;
		display: flex;
		justify-content: center;
		align-items: center;
		height: 1.25em;
		width: 1.25em;
		border-radius: 0.05em;
		color: #fff;
		font-family: sans-serif;
		font-size: 2rem;
		background: var(--clr-accent-red);
		cursor: pointer;

		&:hover,
		&:focus {
			transform: scale(1.1);
			box-shadow: 0 0 0.2em #fff;
		}
	}

	&__header {
		position: relative;
		justify-content: flex-end;
		align-items: center;
		padding-left: 2em;
		padding-right: 1em;
		border-radius: 0.2em;
		background: var(--clr-sky-blue);
		display: none;

		&__image {
			display: none;
			width: 70px;
			background: #fff;
			padding: 0;
			margin: 0 0.25em;
			border-radius: 0.2em;

			img {
				border-radius: 0.2em;
			}
		}

		&__heading {
			position: absolute;
			width: 100%;
			margin: 0;
			font-size: 1.75rem;
			text-transform: uppercase;
			text-align: center;
			color: #fff;
			display: none;

			@media screen and (min-width: 700px) {
				display: inline-block;
			}
		}

		.find-out-more-button {
			margin-top: 0.25em;
			margin-bottom: 0.25em;
		}
	}

	&__main-content {
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		overflow-x: hidden;
		overflow-y: auto;
		background-color: var(--clr-offwhite);
		border-radius: 0.2em;
		box-shadow: 0 0 0.5em #000;
		border: transparent;
		padding: 0 0 3em 0;

		&__reordered-content {
			display: flex;
			flex-direction: column;
			width: 100%;

			&>*:nth-child(1) {
				order: 1;
			}

			&>*:nth-child(2) {
				order: 0;
			}
		}

		&__heading {
			padding: 0 0.5em;
			color: darkgoldenrod;
			display: inline-block;

			&--life-stage-brokerage {
				color: #000;
			}

			&__contract-now-button,
			&__find-out-more-button {
				float: right;
				margin: 1em;
				margin-top: 0.5em;

				@media screen and (min-height: 500px) and (min-width: 390px) {
					display: none;
				}
			}
		}

		&__banner-video {
			background-color: pink;
			height: 329px;
		}
	}


	&__products {
		padding: 0 0.5em;
	}

	&__product-categories {
		list-style: none;
		margin-left: -2.3em;
	}

	&__product-category__heading {
		text-transform: uppercase;
	}

	&__product-subcategory__heading {
		margin-bottom: 0.5em;
	}

	&__product-subcategory__subheading {
		all: unset;
		display: block;
		margin: 0.5em 1.5em;
	}

	&__product-specific {
		list-style: none;

		&::before {
			content: "\2022";
			color: #000;
			font-weight: bold;
			display: inline-block;
			width: 1em;
			margin-left: -1em;
		}
	}

	&--annuities {
		&__main-content__big-button-cards {
			display: flex;
			flex-wrap: wrap;
			gap: 1em;
			margin: 1em;
		}
	}

	&--medicare,
	&--champ-health,
	&--metals {
		.category-modal__main-content__hero-image-right {
			margin: 1em 0;

			@media screen and (min-width: 543px) {
				margin: 1em;
			}
		}

		.modal-link-button-flex-container {
			display: flex;
			flex-wrap: wrap;
			gap: 1em;
			margin: 1em;
		}
	}

	&--champ-health {
		h2 {
			margin-left: 0.5em;
		}

		.ul-flex-container {
			display: flex;
			flex-wrap: wrap;
			margin-bottom: 1em;

			&>* {
				margin: 0;
				flex-grow: 1;
			}
		}
	}

	&--alternatives {
		&__alternative-cards {
			display: flex;
			flex-direction: column;
			gap: 2em;
			margin: 1em;


			&>* {
				padding: 0.5em;
				font-weight: bold;
				text-decoration: none;
				border-top: #ddd solid 1px;
			}

		}
	}

	&--life {
		&__case-consultation-buttons-container {
			display: flex;
			flex-wrap: wrap;
			gap: 1rem;
			margin: 1rem;
		}
	}


	&--life-stage-advisor__main-content {
		background-color: var(--clr-offwhite);
	}
}

.insco-modal {
	&__header {
		display: flex;

	}
}

.find-out-more-modal,
.fi-modal,
.life-case-consultation-request-modal,
.life-case-joint-consultation-request-modal,
.annuity-case-consultation-request-modal,
.annuity-case-joint-consultation-request-modal,
.annuity-myga-rates-modal,
.disability-case-consultation-request-modal,
.disability-case-joint-consultation-request-modal,
.ltc-case-consultation-request-modal,
.ltc-case-joint-consultation-request-modal,
.medicare-case-consultation-request-modal,
.champ-health-case-consultation-request-modal,
.group-benefits-business-consultation-request-modal,
.medmutualprotect-case-consultation-request-modal,
.gwic-case-consultation-request-modal,
.metals-case-consultation-request-modal {
	&__main-content {
		top: 5px;

		&__heading {
			display: flex;
			justify-content: space-between;
			flex-direction: column;
			font-weight: bold;
			font-size: 1.5rem;
			margin: 0.5em 0;

			&--life-stage-brokerage {
				color: var(--clr-info-blue);
			}
		}

		&__image-group {
			display: flex;
			flex-direction: column;
		}

		&__lsb-logo-image,
		&__medmutualprotect-logo-image,
		&__gwic-logo-image,
		&__ray-main-image,
		&__chris-weston-image,
		&__john-baumhover-image,
		// &__leon-hiracheta-image,
		&__tyler-brooks-image,
		&__randy-rowray-image,
		&__brittney-shaw-image,
		&__josh-strause-image,
		&__ginger-burke-image,
		&__brian-thompson-image,
		&__aaron-peterson-image,
		&__aaron-thompson-image,
		&__nathan-akers-image,
		&__mike-reisel-image,
		&__gold-silver-webinar-image {
			object-position: center;
			border-radius: 0.2em;
			margin: 0.5em 1em;
			width: 150px;
		}

		&__tyler-brooks-image,
		&__randy-rowray-image,
		&__brittney-shaw-image,
		&__josh-strause-image,
		&__ginger-burke-image,
		&__brian-thompson-image,
		&__aaron-peterson-image,
		&__aaron-thompson-image,
		&__nathan-akers-image,
		&__mike-reisel-image,
		&__gold-silver-webinar-image {
			margin-left: 0;
			width: 100px;
		}

		&__lsb-logo-image,
		&__medmutualprotect-logo-image,
		&__gwic-logo-image {
			margin: 1em;
			margin-left: auto;
			width: 120px;
			display: none;
		}

		&__medmutualprotect-logo-image,
		&__gwic-logo-image {
			margin-left: auto;
			width: 100%;
			max-width: 200px;

			@media screen and (min-width: 750px) {
				display: unset;
			}

			@media screen and (min-width: 840px) {
				max-width: 300px;
			}
		}

		&__bio {
			padding: 0 1em;
			width: 100%;

			&__card {
				display: inline-flex;
				align-items: flex-start;
				margin-left: 1em;
			}

			&__heading {
				padding: 0 !important;
				margin: 0;
			}

			&__tnbc-logo {
				width: 50px;
			}
		}

		.life-case-consultation-request-form-shell,
		.life-case-joint-consultation-request-form-shell,
		.annuity-case-consultation-request-form-shell,
		.annuity-case-joint-consultation-request-form-shell,
		.disability-case-consultation-request-form-shell,
		.disability-case-joint-consultation-request-form-shell,
		.ltc-case-consultation-request-form-shell,
		.ltc-case-joint-consultation-request-form-shell,
		.medicare-case-consultation-request-form-shell,
		.champ-health-case-consultation-request-form-shell,
		.group-benefits-business-consultation-request-form-shell,
		.medmutualprotect-case-consultation-request-form-shell,
		.gwic-case-consultation-request-form-shell,
		.metals-case-consultation-request-form-shell,
		._2022-college-basketball-viewing-event-rsvp-form-shell,
		.life-stage-advisor-consultation-request-form-shell {
			margin: 1em;
			width: unset;
		}
	}


	.life-case-consultation-request-modal,
	.annuity-case-consultation-request-modal,
	.ltc-case-consultation-request-modal,
	.medicare-case-consultation-request-modal,
	.champ-health-case-consultation-request-modal,
	.group-benefits-business-consultation-request-modal,
	.medmutualprotect-case-consultation-request-modal,
	.gwic-case-consultation-request-modal,
	.metals-case-consultation-request-modal {
		&__main-content {
			&__bio {
				display: flex;
				flex-direction: column;
			}
		}
	}

	.disability-case-consultation-request-modal,
	.disability-case-joint-consultation-request-modal,
	.life-case-joint-consultation-request-modal,
	.annuity-case-consultation-request-modal,
	.annuity-case-joint-consultation-request-modal,
	.annuity-myga-rates-modal,
	.ltc-case-consultation-request-modal,
	.ltc-case-joint-consultation-request-modal,
	.medicare-case-consultation-request-modal,
	.champ-health-case-consultation-request-modal,
	.group-benefits-business-consultation-request-modal,
	.medmutualprotect-case-consultation-request-modal,
	.gwic-case-consultation-request-modal,
	.metals-case-consultation-request-modal {
		@media screen and (min-width: 580px) {
			&__main-content {
				&__lsb-logo-image {
					display: unset;
					float: right;
				}
			}
		}

		/*tt*/
	}

	.disability-case-consultation-request-modal {
		&__main-content {
			&__tyler-brooks-image {
				margin-left: 1em;
			}
		}

		@media screen and (min-width: 635px) {
			&__main-content {
				&__lsb-logo-image {
					display: unset;
					float: right;
				}
			}
		}
	}

	.annuity-case-consultation-request-modal,
	.ltc-case-consultation-request-modal,
	.medicare-case-consultation-request-modal,
	.champ-health-case-consultation-request-modal,
	.group-benefits-business-consultation-request-modal,
	.medmutualprotect-case-consultation-request-modal,
	.gwic-case-consultation-request-modal,
	.metals-case-consultation-request-modal {
		&__main-content {
			&__bio {
				&__card {
					width: 100%;
				}
			}
		}
	}

	@media screen and (min-width: 350px) {

		&__main-content {

			&__ray-main-image,
			&__chris-weston-image,
			&__john-baumhover-image,
			// &__leon-hiracheta-image,
			&__tyler-brooks-image,
			&__randy-rowray-image,
			&__brittney-shaw-image,
			&__josh-strause-image,
			&__ginger-burke-image,
			&__brian-thompson-image,
			&__aaron-peterson-image,
			&__aaron-thompson-image,
			&__nathan-akers-image,
			&__mike-reisel-image,
			&__gold-silver-webinar-image {
				float: left;
			}
		}
	}



	@media screen and (min-width: 960px) {
		&__main-content {
			&__heading {
				flex-direction: row;
			}
		}
	}
}


.annuity-myga-rates-modal {
	&__main-content__pdf-list__item {
		a {
			display: inline-block;

			&:hover {
				font-weight: bold;
			}
		}
	}
}

.information-request-modal,
.tnbc-case-consultation-request-modal,
.mike-reisel-conversation-request-modal {
	display: flex;
	justify-content: center;
	z-index: 100;
	min-width: 285px;
	margin-top: 0;
	background: rgba(black, 0.6);
	overflow-y: scroll;

	a {
		text-decoration: none;
	}

	&__close-button {
		position: unset;
	}

	&__header {
		display: none;
	}

	&__main-content {
		position: unset;
		margin: auto;
		margin-top: clamp(0%, 2.5vw, 100px);
	}

	.information-request-form,
	.tnbc-case-consultation-request-form,
	.mike-reisel-conversation-request-form {
		img {
			width: 100%;
			max-width: 250px;
			float: right;
			margin-left: 1em;
		}

		&-shell {
			width: 90%;

			&__status-message {
				color: var(--clr-info-blue);

				img {
					width: 100%;
					max-width: 250px;
					float: right;
					margin-left: 1em;
				}
			}
		}

		&__information-topic-name-placeholder {
			font-weight: bold;
			color: var(--clr-info-blue);
		}
	}
}

.insco-modal {

	&--tnbc,
	&--apasi,
	&--blp {
		.insco-modal {
			&__header {
				position: relative;
				z-index: 1;
				justify-content: flex-start;
				padding: unset;
				background: #fff;
				min-height: 80px;

				&__image {
					display: flex;
					min-width: 150px;
					margin: 0.25em;
				}

				&__heading {
					all: unset;
					font-size: 0.75rem;
					color: var(--clr-sky-blue);
				}
			}

			&__main-content {
				top: 80px;
			}
		}
	}

	&__main-content {
		p {
			margin-left: 1em;
		}
	}
}

.insco-modal {

	&--apasi,
	&--blp {
		.insco-modal {
			&__header {
				&__image {
					width: 100%;
					max-width: 150px;
				}
			}

			&__main-content {
				h3 {
					font-size: 1.5rem;
					color: var(--clr-sky-blue);
				}

				h4 {
					color: var(--clr-sky-blue);
				}
			}
		}
	}

	&--blp__video {
		aspect-ratio: 16 / 9;
		width: 100%;
	}
}

.find-out-more-modal,
.fi-modal,
.webinar-gold-silver-modal,
.webinar-gold-silver-introduction-modal,
.ryans-story-modal,
.category-modal,
.service-modal,
.insco-modal {
	&__main-content {
		&__heading {
			margin-bottom: 0;
		}

		h3 {
			padding: 0 1em;
		}

		li,
		h4,
		p {
			padding: 0 1.5em 0 1em;

			em {
				color: darkgoldenrod;
				font-weight: bold;

				&::after {
					content: "\A0"; //&nbsp;
				}
			}

			strong {
				font-weight: normal;
				text-decoration: underline;
				background-color: #ffff00;
			}
		}

		li {
			em {
				font-weight: normal;
				text-decoration: underline;
			}
		}

		h5 {
			margin: 0;
			padding: 0 1em;
			color: var(--clr-info-blue);
			font-size: 1rem;
			font-weight: bold;

			&+ol {
				li {
					padding-left: 0.5em;
				}
			}
		}

		a {
			color: blue;

			&:hover {
				transform: scale(1.01);
			}
		}

		.insco-products-cards,
		.state-cards {
			display: flex;
			align-items: center;
			flex-wrap: wrap;

			.insco-products-card,
			.state-card {
				display: flex;
				justify-content: center;
				align-items: center;
				text-align: center;
				height: 150px;
				width: 150px;
				margin: 1em;
				background: transparent;
				background-position: center;
				background-size: contain;
				background-repeat: no-repeat;

				&--illinois-western {
					background-image: url("../images/state-silhouettes/state-illinois.png");
				}

				&--illinois-central {
					background-image: url("../images/state-silhouettes/state-illinois.png");
				}

				&--illinois-chicago {
					background-image: url("../images/state-silhouettes/state-illinois-chicago.png");
				}

				&--iowa-western {
					background-image: url("../images/state-silhouettes/state-iowa.png");
				}

				&--iowa-central {
					background-image: url("../images/state-silhouettes/state-iowa.png");
				}

				&--iowa-eastern {
					background-image: url("../images/state-silhouettes/state-iowa.png");
				}

				&--iowa-illinois-quad-cities {
					background-image: url("../images/state-silhouettes/state-iowa-illinois-quad-cities.png");
				}

				&--kansas {
					background-image: url("../images/state-silhouettes/state-kansas.png");
				}

				&--kansas-missouri-kansas-city {
					background-image: url("../images/state-silhouettes/states-kansas-missouri-kansas-city.png");
				}

				&--michigan {
					background-image: url("../images/state-silhouettes/state-michigan.png");
				}

				&--michigan-detroit {
					background-image: url("../images/state-silhouettes/state-michigan-detroit.png");
				}

				&--minnesota {
					background-image: url("../images/state-silhouettes/state-minnesota.png");
				}

				&--minnesota-twin-cities {
					background-image: url("../images/state-silhouettes/state-minnesota-twin-cities.png");
				}

				&--missouri {
					background-image: url("../images/state-silhouettes/state-missouri.png");
				}

				&--missouri-st-louis {
					background-image: url("../images/state-silhouettes/states-missouri-illinios-st-louis.png");
				}

				&--nebraska {
					background-image: url("../images/state-silhouettes/state-nebraska.png");
				}

				&--nebraska-omaha {
					background-image: url("../images/state-silhouettes/state-nebraska-omaha.png");
				}

				&--south-dakota {
					background-image: url("../images/state-silhouettes/state-south-dakota.png");
				}

				&--wisconsin {
					background-image: url("../images/state-silhouettes/state-wisconsin.png");
				}

				&--wisconsin-milwaukee {
					background-image: url("../images/state-silhouettes/state-wisconsin-milwaukee.png");
				}

				&--tnbc {
					background-image: url("../images/state-silhouettes/logo-tnbc-vanbridge.png");
				}

			}

			.insco-products-card {
				height: 75px;
			}
		}
	}

	&--training {
		&__mike-reisel-section {
			display: flex;
			align-items: flex-start;
			margin: 0 1em;

			&__image {
				min-width: 100px;
			}

			&__button {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 100%;
				max-width: 250px;
				height: calc(1.5em + 0.75rem + 2px);
				cursor: pointer;
				font-weight: bold;
				text-decoration: none;
				color: #fff !important;
				background: var(--clr-info-blue);
				border: none;
				font-size: 0.9rem;
				line-height: 1.5;
				border-radius: 0.25rem;
				margin: 1em auto;
				padding: 0 0.5em;
				position: relative;

				&:hover {
					box-shadow: 0 0 3px #000;
				}

				&__text {
					position: absolute;
					z-index: 1;
				}

				&__mike-reisel-image {
					position: absolute;
					z-index: 0;
					left: 60%;
				}
			}
		}
	}

	&--life {
		&__vive-video {
			width: 32.5em;

			@media screen and (min-width: 1140px) {
				width: 31.2em;

				&-container {
					float: right;
				}
			}
		}
	}

	&__apasi-video {
		max-width: 535px;

		display: block;
		margin: 0;

		@media screen and (min-width: 543px) {
			display: unset;
			margin-right: 0.25em;
			padding: 1em;
			float: right;
		}
	}
	.service-modal--marketing-resources {
		&__resource-card-container {
			display: grid;
			grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
			gap: 1em;
			margin-inline: 1em;
		}
		&__resource-card {
			border: 1px solid var(--clr-gold);
			padding: 1em;
			max-width: 300px;
			text-decoration: unset;
			color: unset;
			background: hsla(60, 100%, 94%, 0.377);
			&:hover {
				border-width: 2px;
			}
			b {
				color: blue;
			}
			button {
				cursor:pointer; padding:0.5em;
			}
		}
		&__description-card {
			li {
				padding-bottom: 1em;
			}
		}
	}
}

.webinar-gold-silver-modal,
.webinar-gold-silver-introduction-modal,
.ryans-story-modal {
	&__main-content {
		top: -0.75em;

		&__heading {
			display: inline-flex;
			justify-content: space-between;
			flex-direction: column;
			font-weight: bold;
			font-size: 1.5rem;
			margin: 0.5em 0.75em;

			&--life-stage-brokerage {
				color: var(--clr-info-blue);
			}
		}

		&__registration-button {
			margin: 1em 1.5em;

			@media screen and (min-width: 1050px) {
				float: right;
			}

		}


		&__link-to-current-webinar {
			display: inline-block;
			margin: 0 2em;

			@media screen and (min-width: 790px) {
				margin: 1.75em 2em;
				float: right;
			}
		}
	}
}


.webinar-gold-silver-introduction-modal {
	&__main-content__narrative {
		margin-top: 1em;

		&__heading {
			color: darkgoldenrod;
		}

		:first-child {
			margin-top: 0;
		}

		@media screen and (min-width: 625px) {
			column-count: 2;
			column-rule: 1px solid lightgray;
		}
	}
}


.ryans-story-modal {
	&__main-content {
		&__lsb-logo-image {
			float: right;
			margin-right: 1em;
		}

		&__heading {
			margin-left: 0;

			@media screen and (min-width: 625px) {
				margin-left: 1em;
			}

		}
	}

	&__story-sections {
		display: flex;
		flex-direction: column;
		gap: 5em;
		line-height: 2;
		clear: both;

		&__newfound-freedom-section {
			min-height: 2200px;

			@media screen and (min-width: 1120px) {
				min-height: 1900px;
			}
		}

		p {
			padding-right: 1em;
		}

		.section-break-symbol {
			width: 100%;
			height: 100px;
		}

		.layout-grid {
			&--images {
				position: relative;
				width: 100%;

				&__ryan-hiking-2020-image {
					margin-inline: 1em;

					@media screen and (min-width: 1120px) {
						position: absolute;
						left: 0;
					}
				}

				&__ryan-and-daughter-hiking-2017-image {
					margin-inline: 1em;

					@media screen and (min-width: 1120px) {
						position: absolute;
						right: 0;
					}
				}

				&__family-2016-image {
					margin-inline: 1em;

					@media screen and (min-width: 1120px) {
						position: absolute;
						top: 30em;
						right: 0;
					}
				}
			}
		}
	}
}


.find-out-more-modal,
.fi-modal {
	&__main-content {
		top: -0.75em;
		min-width: 220px;

		&__heading {
			display: inline-flex;
			justify-content: space-between;
			flex-direction: column;
			font-weight: bold;
			font-size: 1.5rem;
			margin: 0.75em;

			&--life-stage-brokerage {
				color: var(--clr-info-blue);
			}
		}

		p {
			padding: unset;
		}

		&__contact-card {
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			margin-left: 1em;

			&__profile-image-wrapper {
				display: flex;
				height: 100px;
				width: 75px;
				transform-origin: left;
				margin-right: 1em;

				img {
					border-radius: 0.2em;
					object-fit: cover;
					object-position: left;
				}

				@media screen and (min-width: 700px) {
					height: 200px;
					width: 150px;
				}
			}

			&__contact-info {
				@media screen and (min-width: 370px) {
					font-size: 1.1rem;
				}

				@media screen and (min-width: 442px) {
					margin: 0;
				}

				@media screen and (min-width: 543px) {
					font-size: 1.2rem;
				}

				@media screen and (min-width: 700px) {
					font-size: 1.7rem;
				}

				@media screen and (min-width: 950px) {
					font-size: 1.8rem;
				}

				@media screen and (min-width: 1050px) {
					font-size: 2rem;
				}
			}
		}

		&__lsb-logo-image {
			margin: 1em;
			width: 100px;
			float: right;

			@media screen and (min-width: 605px) {
				display: unset;
			}
		}

		.find-out-more-request-form-shell {
			margin: 0 1em;
			width: unset;

			&__form-header,
			&__form-footer {
				padding: 0;
			}

			&__form-header {
				padding-bottom: 0.5em;
			}
		}

		@media screen and (min-width: 700px) {
			h3 {
				&+p {
					font-size: 1.4rem;
				}
			}
		}
	}

	&__follow-us-link {
		text-decoration: none;
		color: #0a66c2 !important;
	}
}

.category-modal--boli {
	.modal-main-content {
		h3 {
			color: darkgoldenrod;
		}
	}
}

.fi-modal,
.life-stage-advisor-modal {
	&__main-content {
		&__heading {
			margin-right: 0;
		}

		&__header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			flex-wrap: wrap;
			gap: 1em;
			margin-right: 1em;
			margin-bottom: 1em;

			&__button-and-logos-wrapper {
				display: flex;
				flex-wrap: wrap;
			}

			&__find-out-more-button {
				min-width: 325px;

				&--1 {
					display: none;

					@media screen and (min-width: 646px) {
						display: flex;
					}

					@media screen and (min-width: 865px) {
						display: none;
					}

					@media screen and (min-width: 1123px) {
						display: flex;
					}
				}

				&--2 {
					@media screen and (min-width: 646px) {
						display: none;
					}

					@media screen and (min-width: 865px) {
						display: flex;
					}

					@media screen and (min-width: 1123px) {
						display: none;
					}
				}
			}


			&__logo-image-wrapper {
				display: flex;
				align-items: center;
				margin-left: -0.5em;

				img {
					display: unset;
					max-height: 40px;
					margin: 0;
				}
			}
		}

		&__contact-card {
			display: block;
			margin-right: 1em;

			&__image-wrapper {
				display: flex;
				height: 100%;
				width: 100%;
				margin-right: 2em;

				.main-image {
					width: 100%;
				}
			}
		}

		&__lsb-logo-image {
			float: unset;

			&.bank-icon-image--top {
				max-width: 50px;
			}
		}

		&__category-link-wrapper {
			display: flex;
			flex-wrap: wrap;
			gap: 1em;
			width: calc(100% - 2em);
			justify-content: space-between;
			margin: 1em;
			color: #fff;

			&>* {
				display: flex;
				justify-content: center;
				align-items: center;
				text-align: center;
				width: 100%;
				min-width: 200px;
				height: 100px;
				border-radius: 0.2em;
				text-decoration: none;
				font-weight: bold;

				&:link,
				&:visited {
					color: #fff;
				}

				&:hover,
				&:focus {
					transform: scale(1.01);
					box-shadow: 0.02em 0.09em 0.5em #fff;
				}

				@media screen and (min-width: 480px) {
					max-width: calc((100% - 2em)/2);
				}

				@media screen and (min-width: 696px) {
					max-width: calc((100% - 2em)/3);
				}

				img {
					width: 100%;
					height: 100%;
					object-fit: cover;
				}
			}
		}

		&__category-link {
			&--boli {
				background: darkgreen;
			}

			&--vive {
				background-image: url("/assets/images/fi/vive-outline2.webp");
				background-position: center;
				background-position-y: -1em;
				background-size: 300px;
				align-items: unset;
			}

			&--final-expense {
				background: brown;
			}

			&--hhc {
				background: darkblue;
			}

			&--annuities {
				background: var(--clr-sky-blue);
			}

			&--ltc {
				background: darkmagenta;
			}
		}

		&__carousel {
			width: calc(100% - 2em);
			margin: 1em;
			text-align: center;
		}

		&__disclosure {
			margin: 1em;
		}
	}
}

.life-stage-advisor-modal {
	&__main-content {
		&__category-link-wrapper {
			gap: 0.5em;
			justify-content: unset;
			place-content: center;

			&>* {
				max-height: 40px;

				&:hover,
				&:focus {
					transform: unset;
				}

				@media screen and (min-width: 480px) {
					max-width: calc((100% - 2em)/2);
				}

				@media screen and (min-width: 696px) {
					max-width: calc((100% - 2em)/4);
				}
			}
		}


		&__category-link {

			&--life {
				background: darkgreen;
			}

			&--final-expense {
				background: hsl(0, 59%, 30%);
			}

			&--dental {
				background: hsl(33, 100%, 34%);
			}

			&--disability {
				background: #00416B;
			}

			&--medicare {
				background: hsl(270, 100%, 25%);
			}

			&--group {
				background: hsl(240, 100%, 40%);
			}
		}
	}
}

.service-modal {
	&--chasing-events {
		&__prices {
			display: flex;
			flex-direction: column;
		}

		&__services-offered {
			display: flex;
			flex-wrap: wrap;
		}

		&__services-pricing-sheet-link {
			display: flex;
			text-decoration: none;
			margin: 0.5em;
			margin-top: 1em;

			&:hover {
				transform: scale(1.02);
				transform-origin: left;
			}

			img {
				width: 50px;
			}

			h3 {
				display: inline-block;
				text-decoration: underline;
			}
		}
	}

	&--ce-resources {
		&__list {
			list-style: none;

			li {
				padding: 0.5em 0;
				font-weight: bold;

				a {
					display: inline-block;
					padding: 1em;
					color: var(--clr-info-blue);
					text-decoration: none;
					border: solid 0.25px #ddd;

					&:hover {
						box-shadow: 0 0 0.2em #ddd;
					}
				}
			}
		}
	}

	&--industry-resources {
		&__list {
			&>li {
				margin-bottom: 1em;
			}

			&__item-heading {
				color: #000 !important;
				padding: 0 !important;
			}

			&__item-list {
				list-style: none;
				padding: 0;
				margin-top: 0.5em;

				li {
					padding: 0.5em 0;
					font-weight: bold;

					a {
						display: inline-block;
						padding: 0.5em;
						color: var(--clr-info-blue);
						text-decoration: none;
						border: solid 0.25px #000;

						&:hover {
							box-shadow: 0 0 0.2em #000;
						}
					}
				}
			}
		}
	}

	&--staffing-resources {
		&__career-opportunity-listings-section {
			.state-card {
				position: relative;

				&__item-count {
					position: absolute;
					bottom: 0;
					left: 0;
					right: 0;
					border-radius: 50%;
					background: var(--clr-dark-blue);
					color: #fff;

					&:empty {
						display: none;
					}
				}
			}
		}
	}
}

.tnbc-what-they-do {
	margin: 0 1em 0.5em 1em;

	h3 {
		margin: unset;
		margin-bottom: 0.3em;
		padding-left: 0.5em;
		font-size: 1.5rem;
		color: var(--clr-info-blue);
	}

	p {
		margin: unset;
		margin-bottom: 1em;
	}

	&__subsection {
		display: flex;
		gap: 1em;
		flex-wrap: wrap;
		justify-content: space-around;
		margin-top: 1em;
		margin-bottom: 1em;
	}
}

.tnbc-what-they-do {
	&-cards {
		background: var(--clr-gold);
		width: 30%;
		min-width: 300px;
		max-height: 450px;
	}

	&-card {
		display: flex;
		align-items: center;

		img {
			width: 80px;
			margin-right: 1em;
		}

		h4 {
			font-size: 1rem;
		}
	}
}


.case-consultation-button,
.current-myga-rates-button,
.lsb-network-production-bonuses-available-button,
.assured-edge-calculator-button,
.care-solutions-calculator-button {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	height: calc(1.5em + 0.75rem + 2px);
	cursor: pointer;
	font-weight: bold;
	text-transform: uppercase;
	text-decoration: none;
	text-align: center;
	color: #fff !important;
	border: none;
	font-size: 0.9rem;
	line-height: 1.5;
	border-radius: 0.25rem;
	margin: 1em;

	&:hover {
		box-shadow: 0 0 3px #000;
	}

	&--champ-health {
		text-transform: unset;
	}
}

.case-consultation-button,
.current-myga-rates-button,
.upcoming-webinar-button,
.lsb-network-production-bonuses-available-button,
.assured-edge-calculator-button,
.care-solutions-calculator-button {
	background: var(--clr-dark-blue);
	max-width: 100%;

	&--apasi {
		background: var(--clr-dark-blue);
		margin-left: 0;
		margin-right: 0;
	}

	&--life,
	&--annuity,
	&--disability,
	&--ltc,
	&--medicare,
	&--champ-health,
	&--metals {
		height: 6.5em;
		width: 20em;
		margin: 0;
		font-size: 0.7rem;


		@media screen and (min-width: 300px) {
			font-size: 0.8rem;
		}

		@media screen and (min-width: 400px) {
			font-size: 1rem;
		}

		@media screen and (min-width: 500px) {
			font-size: 1.3rem;
		}

		&--joint {
			background: var(--clr-success-green);
			padding-right: 1em;
		}
	}

	&--life {
		width: 25em;
	}

	&--annuity {
		margin-top: 0;
	}

	&--disability {
		display: inline-flex;
		margin-right: 1em;
		margin-bottom: 1em;
	}
}

.current-myga-rates-button {
	&__arrow-chart-image {
		margin-right: -0.5em;
		margin-left: -1em;
	}
}

.assured-edge-calculator-button,
.care-solutions-calculator-button {

	&__aig-image,
	&__oneamerica-image {
		max-width: 100px;
		margin-right: 0.25em;
		margin-left: 1em;
	}

	&__oneamerica-image {
		max-width: 150px;
	}
}


.ryans-story-button {
	display: block;
	max-width: 360px;
	float: left;
	margin-right: 1em;
}

.lsb-network-production-bonuses-available-button,
.assured-edge-calculator-button,
.care-solutions-calculator-button {
	background: var(--clr-sky-blue);
	margin-top: 0;
	padding: 2em 0;

	&--apasi {
		margin-left: 0;
		margin-right: 0;
	}

	&--apasi {
		margin-top: 2.5em;
		margin-bottom: 0;
	}
}

.assured-edge-calculator-button,
.care-solutions-calculator-button {
	background: var(--clr-dark-blue);
}

.upcoming-webinar-button {
	width: 20em;

	&--metals {
		display: grid;
		grid-template-columns: 1fr 1fr;
	}

	@media screen and (min-width: 300px) {
		font-size: 0.8rem;
	}

	@media screen and (min-width: 400px) {
		font-size: 1rem;
	}

	@media screen and (min-width: 500px) {
		font-size: 1.3rem;
	}
}

.tnbc-new-agent-portal-request-form-shell,
.tnbc-case-consultation-request-form-shell,
.mike-reisel-conversation-request-form-shell,
.apasi-new-agent-contract-request-form-shell,
.blp-new-agent-contract-request-form-shell,
.life-case-consultation-request-form-shell,
.life-case-joint-consultation-request-form-shell,
.annuity-case-consultation-request-form-shell,
.annuity-case-joint-consultation-request-form-shell,
.disability-case-consultation-request-form-shell,
.disability-case-joint-consultation-request-form-shell,
.ltc-case-consultation-request-form-shell,
.ltc-case-joint-consultation-request-form-shell,
.medicare-case-consultation-request-form-shell,
.champ-health-case-consultation-request-form-shell,
.group-benefits-business-consultation-request-form-shell,
.medmutualprotect-case-consultation-request-form-shell,
.gwic-case-consultation-request-form-shell,
.metals-case-consultation-request-form-shell,
.find-out-more-request-form-shell,
.information-request-form-shell,
._2022-college-basketball-viewing-event-rsvp-form-shell,
.life-stage-advisor-consultation-request-form-shell {
	border: 2px dashed red;
	background: #fff;
	margin: auto;
	padding: 1em;
	width: 100%;

	&__response-message {
		font-weight: bold;
		background-color: #fff;
		padding: 10px;
		display: none;
	}

	&__progress-spinner {
		font-size: 5em;
		color: #0000d0;
		display: none;
	}

	&__form-header,
	&__form-footer {
		color: var(--clr-info-blue);
	}

	&__form-header {
		font-weight: bold;
		letter-spacing: -1px;
		margin-top: 0;
		margin-bottom: 0.5em;
		display: inline;
	}

	&__form-footer {
		margin: 0;
		margin-top: 1em;
	}

	.form-group {
		transition: all 0.2s;
		cursor: text;

		&:focus-within {
			transform: scale(1.03, 1.03);
		}

		&--attachments {
			position: relative;

			&__list {
				margin: 0 1em;
				position: relative;
				top: -2em;
				font-size: 0.8rem;

				&__item__remove {
					color: red;

					&:hover {
						font-weight: bold;
					}
				}
			}

			&:focus-within {
				transform: unset;
			}
		}

	}

	.form-label {
		display: block;
		opacity: 1;
		font-size: 1em;
		color: #aaa;
		transform: translateY(-1.9em);
		transform-origin: top left;
		transition: all 0.2s;
		cursor: text;
		pointer-events: none;
	}

	.form-control {
		display: block;
		width: 100%;
		height: calc(1.5em + 0.75rem + 2px);
		padding: 0.375em 1em;
		margin: 0;
		font-size: 1rem;
		font-weight: 400;
		line-height: 1.5;
		background: #fff;
		background-clip: padding-box;
		border: 1px solid #a6acb3;
		border-radius: 0;
		border-style: none none solid none;
		appearance: none;

		&[type="date"] {
			font-family: 'Poppins', sans-serif;
			font-size: 0.9rem;
		}

		&:hover {
			border-bottom: solid 2px green;
		}

		&:focus {
			outline-style: none;
			box-shadow: none;
			border-bottom-color: blue;
		}

		&::placeholder {
			color: transparent;
		}

		&:focus+.form-label,
		&:not(:placeholder-shown)+.form-label {
			transform: translateY(-3em) scale(0.8);
		}

		&--attachments {
			margin-top: 0.5em;
			border-bottom: none;
			cursor: pointer;

			&:focus+.form-label,
			&:not(:placeholder-shown)+.form-label {
				transform: translateY(-3.6em) scale(0.8);
			}

			&:hover {
				border-bottom: none;
			}
		}
	}

	.form-label--attachments__clear-button {
		pointer-events: all;
		cursor: pointer;
		padding: 0.25em;
		font-size: 1.1rem;
		display: none;
	}


	&__form-submit-button {
		display: block;
		width: 100%;
		height: calc(1.5em + 0.75rem + 2px);
		cursor: pointer;
		font-weight: bold;
		text-transform: uppercase;
		color: #fff;
		background: var(--clr-success-green);
		border: none;
		font-size: 1.2rem;
		line-height: 1.5;
		border-radius: 0.25rem;

		&:hover {
			box-shadow: 0 0 3px #000;
		}

		&__your-text {
			display: none;

			@media screen and (min-width: 330px) {
				display: unset;
			}
		}
	}
}

.life-stage-advisor-consultation-request-form-shell {
	border: unset;
	background: #fff;
	box-shadow: 1px 1px 3px #000;

	padding: 1em;
	width: unset;

}

.group-benefits-business-consultation-request-modal {
	&__main-content {
		&__bio__card {
			margin-left: 0;
		}

		&__chris-weston-image {
			margin-left: 0;
		}
	}

	.group-benefits-business-consultation-request-form-shell {
		border: unset;
		margin: 0;

		&__form-header {
			display: block;
			margin-bottom: 1em;
		}


		.form-group,
		fieldset {
			transition: unset;

			&:focus-within,
			&:hover {
				transform: unset;
				color: green;
				// font-weight: bold;
			}
		}

		fieldset {
			margin: 0;
			padding-left: 0;
			padding-bottom: 2em;
			border-inline: none;
			border-bottom: none;

			&.single-input-fieldset {
				margin-top: 0;
				margin-bottom: 0;
				border-inline: none;
				border-bottom: none;
				padding: 0;
				padding-top: 1em;

				&--referred-by {
					border: 1px solid gray;
					padding: 1em;
					margin: 1em;
				}

				input {
					// border: 1px solid red;
					padding-left: 1em;

					&:focus {
						border-bottom: 1px solid green;
					}

					&#referredBy {
						// border: 1px solid gray;
						// border-top: none;
					}
				}
			}


			&>.form-control {
				border-bottom: none;
			}

			&>.radio-with-textbox {
				input[type="text"] {
					font-size: 1.1rem;
					height: 1.5em;
				}
			}

			&>.checkbox-option-group {
				display: flex;
				align-items: center;
				margin-left: 2em;
			}


			input[type="checkbox"],
			input[type="radio"] {
				width: 1.8em;
				height: 1.8em;
			}
		}

		.form-control {
			padding-left: 0;
		}

		.form-label {
			display: unset;
			color: unset;
			transform: unset;
			transition: unset;
		}

		&__form-submit-button {
			text-transform: unset;
		}

	}
}


.apasi-new-agent-contract-request-form-shell,
.blp-new-agent-contract-request-form-shell {
	max-width: 500px;
	margin-bottom: 2em;
}

.tnbc-why-cards {
	display: flex;
	gap: 0.5em;
	flex-wrap: wrap;

	.tnbc-why-card {
		width: 100%;
		padding: 1em;
		background: #00859e;
		color: #fff;

		h4 {
			margin: unset;
			margin-bottom: 0.3em;
			font-size: 1.2rem;
		}

		p {
			margin: unset;
		}
	}
}

.tnbc-testimonials-section {
	min-height: 150px;
	margin: 0 1em;

	&__carousel {
		background: #00859e;
		color: #fff;
		padding: 0.2em 0.25em;
		margin: 0;
	}
}

//=======================================================
// Insco modals - height-widths====================
@media screen and (min-height: 500px) and (min-width: 208px) {
	.insco-modal {

		&--tnbc,
		&--apasi,
		&--blp {
			.insco-modal {
				&__main-content {
					top: 55px;
				}
			}
		}
	}
}

@media screen and (min-height: 500px) and (min-width: 275px) {
	.insco-modal {

		&--tnbc,
		&--apasi,
		&--blp {
			.insco-modal {
				&__main-content {
					top: 60px;
				}
			}
		}
	}
}

@media screen and (min-height: 500px) and (min-width: 305px) {
	.insco-modal {

		&--tnbc,
		&--apasi,
		&--blp {
			.insco-modal {
				&__main-content {
					top: 65px;
				}
			}
		}
	}
}

@media screen and (min-height: 500px) and (min-width: 340px) {
	.insco-modal {

		&--tnbc,
		&--apasi,
		&--blp {
			.insco-modal {
				&__main-content {
					top: 70px;
				}
			}
		}
	}
}

@media screen and (min-height: 500px) and (min-width: 350px) {
	.insco-modal {

		&--tnbc,
		&--apasi,
		&--blp {
			.insco-modal {
				&__main-content {
					top: 75px;
				}
			}
		}
	}
}

@media screen and (min-height: 500px) and (min-width: 390px) {

	.category-modal,
	.service-modal,
	.insco-modal {
		&__header {
			justify-content: space-between;
			display: flex;

			&__image {
				display: flex;
			}

			&__background-image {
				left: 150px;
			}

			&__heading {
				font-size: 1.75rem;
			}
		}

		&.no-header-image &__header {
			padding-left: 1em;
		}

		&.no-header-image &__main-content {
			top: 90px;
		}
	}

	.category-modal,
	.service-modal {
		&__main-content {
			top: 57px;

			&__contract-now-button {
				font-size: 1.3rem;
			}
		}
	}
}

@media screen and (min-width: 390px) {
	.insco-modal {

		&--tnbc,
		&--apasi,
		&--blp {
			.insco-modal {
				&__header {
					&__heading {
						font-size: 1rem;
						padding-left: 1em;
					}
				}

				&__main-content {
					top: 65px;
				}
			}
		}
	}
}

@media screen and (min-width: 410px) {
	.insco-modal {

		&--tnbc,
		&--apasi,
		&--blp {
			.insco-modal {
				&__main-content {
					top: 70px;
				}
			}
		}
	}
}

@media screen and (min-width: 440px) {
	.insco-modal {

		&--tnbc,
		&--apasi,
		&--blp {
			.insco-modal {
				&__main-content {
					top: 75px;
				}
			}
		}
	}
}

@media screen and (min-width: 470px) {
	.insco-modal {

		&--tnbc,
		&--apasi,
		&--blp {
			.insco-modal {
				&__main-content {
					top: 80px;
				}
			}
		}
	}
}

@media screen and (min-width: 500px) {
	.insco-modal {

		&--tnbc,
		&--apasi,
		&--blp {
			.insco-modal {
				&__main-content {
					top: 85px;
				}
			}
		}
	}
}

@media screen and (min-width: 660px) {
	.insco-modal {

		&--tnbc,
		&--apasi,
		&--blp {
			.insco-modal {
				&__header {
					&__heading {
						font-size: 1.2rem;
						padding-left: 2em;
					}
				}
			}
		}
	}
}

@media screen and (min-width: 700px) {
	.insco-modal {

		&--tnbc,
		&--apasi,
		&--blp {
			.insco-modal {
				&__header {
					&__heading {
						font-size: 1.3rem;
					}
				}
			}
		}
	}
}

@media screen and (min-width: 780px) {
	.insco-modal {

		&--tnbc,
		&--apasi,
		&--blp {
			.insco-modal {
				&__header {
					&__heading {
						font-size: 1.4rem;
						padding: 0;
						padding-right: 150px;
						margin: 0 auto;
					}
				}
			}
		}

		&--blp {
			.insco-modal {
				&__header {
					&__heading {
						padding-right: 0;
					}
				}
			}
		}
	}
}

@media screen and (min-height: 500px) and (min-width: 875px) {

	.category-modal,
	.service-modal,
	.insco-modal {
		&__header {

			&__find-out-more-button,
			&__contract-now-button {
				transform-origin: right;
			}
		}

		&.no-header-image &__header__find-out-more-button,
		&.no-header-image &__header__contract-now-button {
			font-size: 1.5rem;
		}
	}
}

@media screen and (min-width: 875px) {
	.insco-modal {

		&--tnbc,
		&--apasi,
		&--blp {
			.insco-modal {
				&__header {
					&__heading {
						font-size: 1.7rem;
					}
				}
			}
		}
	}

	.tnbc-what-they-do {
		&__subsection {
			margin-left: 4em;
		}
	}
}

@media screen and (min-height: 500px) and (min-width: 990px) {
	.insco-modal {
		&__header {
			&__heading {
				font-size: 3rem;
			}
		}
	}
}

@media screen and (min-width: 990px) {
	.insco-modal {

		&--tnbc,
		&--apasi,
		&--blp {
			.insco-modal {
				&__header {
					&__heading {
						font-size: 2rem;
					}
				}
			}
		}
	}
}

@media screen and (min-height: 500px) and (min-width: 1020px) {
	.insco-modal {
		&__header {
			&__heading {
				font-size: 3rem;
			}
		}
	}
}

//===========================================
// Insco modals - widths====================

@media screen and (min-width: 543px) {

	.category-modal,
	.insco-modal,
	.service-modal,
	.life-case-joint-consultation-request-modal {
		&__main-content {
			&__hero-image-left {
				max-width: 60%;
				float: left;
				margin-right: 1em;
				margin-bottom: 1em;
			}

			&__hero-image-right {
				max-width: 60%;
				float: right;
				margin-left: 1em;
				margin-bottom: 1em;
			}

			&__reordered-content {
				display: unset;
			}
		}

		&--economic-update {
			.service-modal__main-content {
				&__hero-image-left {
					max-width: 60%;
					float: left;
					margin-right: 1em;
				}

				&__hero-image-right {
					max-width: 60%;
					float: right;
					margin-left: 1em;
				}

				&__reordered-content {
					display: unset;
				}
			}
		}

		&--exec-coaching {
			&__schedule-conversation {
				a {
					display: inline-block;

					&:hover {
						transform: scale(1.02);
					}
				}
			}
		}

		&--event-planning {
			&__chasing-events-link {
				display: inline-block;

				&:hover {
					transform: scale(1.02);
					transform-origin: left;
				}
			}
		}

		&--apasi,
		&--blp {
			.insco-modal__main-content {
				&__reordered-content {
					display: flex;
				}
			}
		}
	}

	.life-case-joint-consultation-request-modal {
		&__main-content {
			&__hero-image-right {
				margin-right: 1em;
			}
		}
	}
}

@media screen and (min-width: 696px) {
	.tnbc-what-they-do {
		&__subsection {
			.tnbc-new-agent-portal-request-form-shell {
				width: 50%;
			}
		}
	}
}

@media screen and (min-width: 700px) {
	.insco-modal {

		&--tnbc,
		&--apasi,
		&--blp {
			&.no-header-image .insco-modal__header {
				flex-wrap: unset;
			}
		}
	}

	.service-modal {
		&--chasing-events {
			&__prices {
				max-width: 75%;
			}
		}
	}
}

@media screen and (min-width: 950px) {
	.tnbc-why-cards {
		flex-wrap: unset;
	}

	.apasi-new-agent-contract-request-form-shell,
	.blp-new-agent-contract-request-form-shell {
		float: right;
		margin: 0.5em 3em;
	}

	.insco-modal {

		&--apasi,
		&--blp {
			.insco-modal__main-content {
				&__reordered-content {
					display: unset;
				}
			}
		}
	}

	@media screen and (min-width: 1020px) {
		.insco-modal {

			&--tnbc,
			&--apasi,
			&--blp {
				&.no-header-image .insco-modal__header {
					flex-wrap: wrap;
					text-align: end;

					&__heading {
						padding-right: 1em;
					}
				}

				&.no-header-image .insco-modal__header__contract-now-button {
					margin-top: 0.025em;
					font-size: 2rem;
				}
			}
		}

		.tnbc-what-they-do {
			&__subsection {
				margin-left: 5em;
			}
		}
	}

	/* test */
}

/* test */