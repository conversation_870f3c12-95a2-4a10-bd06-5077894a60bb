<?php
define('TITLE', 'Your Life Stage Advisor');
define('CANONICAL_URL', 'https://www.lifestagebrokerage.com/advisor');
define('META_DESCRIPTION', 'Life Stage Brokerage Network: dedicated insurance professionals offering assistance in Life and Health insurance markets');
define('META_KEYWORDS', '');
require_once(APP_ROOT . '/views/partials/_head.php');
?>

<div class="modal modal--life-stage-advisor">
	<div class="modal--life-stage-advisor__main-content modal__main-content">
		<img loading="lazy" class="main-image" src="/assets/images/advisor/fi-team-photo2.webp" alt="advisor team photo">
		<h2 style="font-weight:normal; margin:unset; font-size: 1rem;">Dedicated Professionals Offering Assistance in Life and Health Insurance Markets</h2>
		<h1 style="display: block;	font-size: 1.8rem;	font-weight: normal;margin: 0.825em 0;">Confused About Insurance Coverage? You’re Not Alone.</h1>
		<p>Many people feel overwhelmed when it comes to understanding their insurance coverage. They’re unsure about what they currently own—or what they should consider. Concerns about pre-existing conditions and qualification rules only add to the stress. And too often, they’re left frustrated after being shuffled from one 800-number to the next.</p>
		<p>At <b>The Life Stage Brokerage Network</b>, we do things differently.</p>
		<p>We believe your stage of life should <em>guide your coverage</em>—not complicate it. With over 100 years of combined experience, our dedicated agents and advisors take the time to walk you through your options, explain what you already have, and help you make confident, informed decisions.</p>

		<h3>Here’s How We Help—Every Step of the Way:</h3>
		<ul>
			<li><b>1.</b>&nbsp;&nbsp;Understand your current situation</li>
			<li><b>2.</b>&nbsp;&nbsp;Review your existing coverage</li>
			<li><b>3.</b>&nbsp;&nbsp;Assess whether your current plan meets your needs</li>
			<li><b>4.</b>&nbsp;&nbsp;Update or replace coverage to better align with your goals</li>
		</ul>

		<h3 class="modal--life-stage-advisor__main-content__please-call-heading">Let’s simplify the process together.<br>
			<b>Call 888.562.3771</b> to schedule a personalized consultation, or fill out the form below to get started.
		</h3>
		<div class="life-stage-advisor-consultation-request-form-shell">
			<h3 class="life-stage-advisor-consultation-request-form-shell__form-header">Request a Consultation</h3>
			<div class="life-stage-advisor-consultation-request-form-shell__progress-spinner fa fa-spinner fa-spin">
			</div>
			<div class="life-stage-advisor-consultation-request-form-shell__status-message request-form-shell__status-message">
			</div>
			<form class="life-stage-advisor-consultation-request-form" id="life-stage-advisor-consultation-request-form" action="#" method="post">
				<input type="hidden" name="async" value="true">
				<input type="hidden" name="requestSource" value="&lt;life-stage-advisor-consultation-request-form&gt;">
				<input type="hidden" name="formId" value="life-stage-advisor-consultation-request-form">
				<div class="form-group">
					<input class="form-control" type="text" id="fullName" name="fullName" placeholder="Your Name & Age *" minlength="5" required>
					<label for="fullName" class="form-label">Your Name & Age *</label>
				</div>
				<div class="form-group">
					<input class="form-control" type="text" id="optionalSpouseName" name="optionalSpouseName" placeholder="Spouse's Name & Age">
					<label for="optionalSpouseName" class="form-label">Spouse's Name & Age</label>
				</div>
				<div class="form-group">
					<input class="form-control" type="tel" id="phoneNumber" name="phoneNumber" placeholder="Phone * ************" minlength="10" required>
					<!-- pattern="[0-9]{3}-[0-9]{3}-[0-9]{4}" -->
					<label for="phoneNumber" class="form-label">Phone *
						&nbsp;&nbsp;&nbsp;<span style="font-style: italic;">************</span></label>
				</div>
				<div class="form-group">
					<input class="form-control" type="email" id="emailAddress" name="emailAddress" placeholder="Email *" pattern="^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$" required>
					<label for="emailAddress" class="form-label">Email *
						&nbsp;&nbsp;&nbsp;<span style="font-style: italic;"><EMAIL></span></label>
				</div>
				<div class="checkbox-option-group">
					<p>Preferred Contact Method:</p>
					<label>
						<input type="checkbox" name="optionalPreferredContactMethod[]" id="optionalPreferredContactMethod_phone" value="phone">
						&nbsp;Phone&nbsp;&nbsp;&nbsp;&nbsp;
					</label>
					<div class="best-time-to-call-option-group">
						<span>Best time to call:&nbsp;&nbsp;</span>
						<label>
							<input type="checkbox" name="optionalBestTimeToCall[]" id="optionalBestTimeToCall_morning" value="morning">
							&nbsp;Morning&nbsp;&nbsp;&nbsp;&nbsp;
						</label>
						<label>
							<input type="checkbox" name="optionalBestTimeToCall[]" id="optionalBestTimeToCall_afternoon" value="afternoon">
							&nbsp;Afternoon&nbsp;&nbsp;&nbsp;&nbsp;
						</label>
						<label>
							<input type="checkbox" name="optionalBestTimeToCall[]" id="optionalBestTimeToCall_evening" value="evening">
							&nbsp;Evening&nbsp;&nbsp;&nbsp;&nbsp;
						</label>
					</div>

					<label>
						<input type="checkbox" name="optionalPreferredContactMethod[]" id="optionalPreferredContactMethod_email" value="email">
						&nbsp;Email&nbsp;&nbsp;&nbsp;&nbsp;
					</label>
				</div>
				<div class="checkbox-option-group">
					<p>Insurance Interests:</p>
					<label>
						<input type="checkbox" name="optionalInsuranceInterest[]" id="optionalInsuranceInterest_life" value="Life Insurance">
						&nbsp;Life Insurance
					</label>
					<label>
						<input type="checkbox" name="optionalInsuranceInterest[]" id="optionalInsuranceInterest_dental" value="Dental">
						&nbsp;Dental
					</label>
					<label>
						<input type="checkbox" name="optionalInsuranceInterest[]" id="optionalInsuranceInterest_disability" value="Disability">
						&nbsp;Disability
					</label>
					<label>
						<input type="checkbox" name="optionalInsuranceInterest[]" id="optionalInsuranceInterest_ltc" value="Long-Term Care">
						&nbsp;Long-Term Care
					</label>
					<label>
						<input type="checkbox" name="optionalInsuranceInterest[]" id="optionalInsuranceInterest_hhc" value="Home Health Care">
						&nbsp;Home Health Care
					</label>
					<label>
						<input type="checkbox" name="optionalInsuranceInterest[]" id="optionalInsuranceInterest_medicare" value="Medicare Supplement">
						&nbsp;Medicare Supplement
					</label>
					<label>
						<input type="checkbox" name="optionalInsuranceInterest[]" id="optionalInsuranceInterest_group" value="Group Coverage">
						&nbsp;Group Coverage
					</label>
					<label>
						<input type="checkbox" name="optionalInsuranceInterest[]" id="optionalInsuranceInterest_annuities" value="Annuities">
						&nbsp;Annuities
					</label>
					<label>
						<input type="checkbox" name="optionalInsuranceInterest[]" id="optionalInsuranceInterest_final-expense" value="Final Expense / Funeral">
						&nbsp;Final Expense / Funeral
					</label>
					<label>
						<input type="checkbox" name="optionalInsuranceInterest[]" id="optionalInsuranceInterest_other" value="other">
						&nbsp;other
					</label>
				</div>
				<div class="form-group">
					<input class="form-control" type="text" id="optionalTellUsMore" name="optionalTellUsMore" placeholder="Tell us more about your situation and concerns">
					<label for="optionalTellUsMore" class="form-label">Tell us more about your situation and concerns</label>
				</div>
				<button name="button" class="life-stage-advisor-consultation-request-form-shell__form-submit-button form-submit-button" title="send your request - someone will contact you shortly">Send</button>
				<h4 class="life-stage-advisor-consultation-request-form-shell__form-footer"></h4>
			</form>
		</div>
		<div class="life-stage-advisor-modal__main-content__category-link-wrapper">
			<div class="life-stage-advisor-modal__main-content__category-link--life">Life Insurance</div>
			<div class="life-stage-advisor-modal__main-content__category-link--dental">Dental</div>
			<div class="life-stage-advisor-modal__main-content__category-link--disability">Disability</div>
			<div class="life-stage-advisor-modal__main-content__category-link--ltc"><span>Long-Term Care</span></div>
			<div class="life-stage-advisor-modal__main-content__category-link--hhc">Home Health Care</div>
			<div class="life-stage-advisor-modal__main-content__category-link--medicare">Medicare Supplement</div>
			<div class="life-stage-advisor-modal__main-content__category-link--group">Group Coverage</div>
			<div class="life-stage-advisor-modal__main-content__category-link--annuities">Annuities</div>
			<div class="life-stage-advisor-modal__main-content__category-link--final-expense">Final Expense / Funeral</div>
		</div>
	</div>
</div>

<?php
require_once(APP_ROOT . '/views/partials/_footer.php');
?>

<style>
	.toast,
	.footer__find-out-more-button {
		display: none;
	}

	.modal--life-stage-advisor__main-content {
		padding: 1em;
	}

	.modal--life-stage-advisor__main-content ul {
		display: grid;
		gap: 1rem;
		font-size: 1.25rem;
		list-style: none;
		margin-left: 1.5rem;
		padding: 0;
	}

	.modal--life-stage-advisor__main-content li {
		text-indent: -1.5rem;
		padding-left: 1.5rem;
	}

	.modal--life-stage-advisor__main-content__please-call-heading {
		font-weight: normal;
		display: inline-block;
		padding-inline: 1rem;
		margin-top: 2rem;
		/* border: 1px solid #C1B390; */
		/* color: darkblue; */
		/* color: black; */
		/* background: #C1B390; */
	}

	.your-stage-in-life-phrase {
		border-radius: 0.25rem;
		background: #C1B390;
		padding-inline: 0.5rem;
	}

	.life-stage-advisor-consultation-request-form-shell {
		margin-bottom: 3em;
	}

	.life-stage-advisor-consultation-request-form-shell__form-header {
		display: block;
		margin-bottom: 1.5em;
		color: darkblue;
	}

	.life-stage-advisor-consultation-request-form-shell__form-submit-button {
		max-width: 300px;
	}

	.life-stage-advisor-consultation-request-form-shell .checkbox-option-group {
		display: grid;
		margin-bottom: 1rem;
	}

	.life-stage-advisor-consultation-request-form-shell .best-time-to-call-option-group {
		display: grid;
		margin-left: 2rem;
		margin-bottom: 1rem;
	}
</style>

<script>
	// Automatically check "Phone" when "morning", "afternoon", or "evening" is checked.
	const preferredContactMethodPhone = document.querySelector('#optionalPreferredContactMethod_phone');
	const bestTimeToCallCheckboxes = document.querySelectorAll('[name="optionalBestTimeToCall[]"]');
	bestTimeToCallCheckboxes.forEach(checkbox => {
		checkbox.addEventListener('change', () => {
			if (checkbox.checked) {
				preferredContactMethodPhone.checked = true;
			}
		});
	});
</script>
<?php
require_once(APP_ROOT . '/views/partials/_foot.php');
?>