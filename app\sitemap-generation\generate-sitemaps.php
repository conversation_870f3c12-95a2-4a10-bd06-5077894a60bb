<?php
// https://github.com/janci/sitemap-generator
//https://github.com/sunra/php-simple-html-dom-parser

require_once('src/SitemapGenerator.php');
require_once('src/functions.php');
require_once('src/IScanDriver.php');
require_once('src/ScanDrivers/StringScanDriver.php');
require_once('src/ScanDrivers/UrlScanDriver.php');
require_once('src/Utils/Strings.php');

use SitemapGenerator\SitemapGenerator;
use SitemapGenerator\ScanDrivers\UrlScanDriver;

function send_sitemap_url_to_google($site, $sitemapFileName)
{
	$data = array("sitemap" => "{$site}/sitemaps/{$sitemapFileName}");
	$ch = curl_init();
	curl_setopt($ch, CURLOPT_URL, "https://www.google.com/ping?" . http_build_query($data));
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
	curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
	curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
	$result = curl_exec($ch);
	curl_close($ch);
	return $result;
}

$siteMapsDirectory = __DIR__ . "/../public_html/sitemaps";
$sites = array(
	// $site => $fileName.
	"http://lifestagebrokerage.com" => "sitemap-http_lifestagebrokerage.xml",
	"http://www.lifestagebrokerage.com" => "sitemap-http_www.lifestagebrokerage.xml",
	"https://lifestagebrokerage.com" => "sitemap-https_lifestagebrokerage.xml",
	"https://www.lifestagebrokerage.com" => "sitemap-https_www.lifestagebrokerage.xml",
	"https://www.lifestagebrokerage.com" => "sitemap-lifestagebrokerage.xml",
);
echo "\nscanning sites....\n\n";

$sitemapGenerator = new SitemapGenerator();

foreach ($sites as $site => $fileName) {
	$sitemapGenerator->scanSite(new UrlScanDriver($site));
	$sitemapXML = $sitemapGenerator->getSitemapContent();
	if (!is_dir($siteMapsDirectory)) {
		echo "$siteMapsDirectory does not exist!\n\n";
		echo "Creating directory.\n\n";
		mkdir($siteMapsDirectory);
	}
	file_put_contents($siteMapsDirectory . "/" . $fileName, $sitemapXML);
	echo "$fileName   created\n";

	// Send site map to various search engines.=======================
	// Google.
	$result = send_sitemap_url_to_google($site, $fileName);
	if ($result) {
		echo "Sitemap submitted to Google.\n";
	}
	//// Bing/MSN.
	//$data = array("sitemap" => "{$site}/sitemaps/{$fileName}");
	//$result = file_get_contents("https://www.bing.com/webmaster/ping.aspx?" . http_build_query($data));
	//if ($result) {
	//	echo "Sitemap submitted to Bing/MSN.\n";
	//}
}
echo "-------finished!\n\n";
exit;
