<?php
// Meilisearch functionality now routed through /meilisearch endpoint
?>


<div id="instantsearch__container">
	<style>
		#instantsearch__container {
			--hght-instantsearch-form: 2.7em;
			pointer-events: auto;
			position: sticky;
			top: 0;
			right: 0;
			z-index: 101;
			background: #fff;
			font-size: 12px;
			width: 100vw;
			max-height: 100vh;
		}

		#instantsearch__container * {
			box-sizing: border-box;
			padding: 0;
			margin: 0;
		}

		#instantsearch__form {
			height: var(--hght-instantsearch-form);
			width: 100%;
			float: right;
		}

		#instantsearch__input {
			width: 100%;
			padding: 0.45em 0.5em;
			border: 1px solid #ccc;
			outline: none;
		}

		#instantsearch__search-icon {
			position: absolute;
			top: 0.25em;
			right: 0.2em;
			padding: 0.5em;
			background: #ccc;
			color: #333;
		}

		#instantsearch__input:not(:placeholder-shown)~#instantsearch__search-icon {
			display: none;
		}

		#instantsearch__clear-button {
			position: absolute;
			top: 0.125em;
			right: 0;
			background: var(--clr-accent-red);
			color: #fff;
			border: none;
			padding: 0.25em 0.5em;
			width: fit-content;
			height: 2em;
			cursor: pointer;
		}

		#instantsearch__clear-button:hover,
		#instantsearch__clear-button:focus {
			background: red;
		}

		#instantsearch__input:placeholder-shown~#instantsearch__clear-button {
			opacity: 0;
			pointer-events: none;
		}

		#instantsearch__results {
			position: absolute;
			top: var(--hght-instantsearch-form);
			right: 0;
			padding: 0 0.5em;
			background: #fff;
			width: fit-content;
			max-height: calc(100vh - var(--hght-instantsearch-form) - var(--hght-footer));
			max-width: 750px;
			overflow-y: auto;
		}

		@media screen and (min-width: 450px) {
			#instantsearch__container {
				border-radius: 0.25em 0 0 0.25em;
				width: unset;
			}

			#instantsearch__form {
				max-width: 24.5em;
			}

			#instantsearch__input {
				display: inline;
				border-radius: 0.25em 0 0 0.25em;
			}
		}

		/*======================================================================
		lds-ellipsis progress indicator ========================================
		=======================================================================*/
		.lds-ellipsis {
			display: inline-block;
			position: absolute;
			right: 0;
			width: 100%;
			height: 80%;
			background: var(--clr-accent-red);
			display: none;
		}

		.lds-ellipsis,
		.lds-ellipsis div {
			box-sizing: border-box;
		}

		.lds-ellipsis div {
			position: absolute;
			top: 0.2em;
			width: 0.75em;
			height: 0.75em;
			border-radius: 50%;
			background: currentColor;
			animation-timing-function: cubic-bezier(0, 1, 1, 0);
		}

		.lds-ellipsis div:nth-child(1) {
			left: 0.5em;
			animation: lds-ellipsis1 0.6s infinite;
		}

		.lds-ellipsis div:nth-child(2) {
			left: 0.5em;
			animation: lds-ellipsis2 0.6s infinite;
		}

		.lds-ellipsis div:nth-child(3) {
			left: 2em;
			animation: lds-ellipsis2 0.6s infinite;
		}

		.lds-ellipsis div:nth-child(4) {
			left: 3.5em;
			animation: lds-ellipsis3 0.6s infinite;
		}

		@keyframes lds-ellipsis1 {
			0% {
				transform: scale(0);
			}

			100% {
				transform: scale(1);
			}
		}

		@keyframes lds-ellipsis3 {
			0% {
				transform: scale(1);
			}

			100% {
				transform: scale(0);
			}
		}

		@keyframes lds-ellipsis2 {
			0% {
				transform: translate(0, 0);
			}

			100% {
				transform: translate(1.5em, 0);
			}
		}

		/* ====================================== */
	</style>



	<form id="instantsearch__form" autocomplete="off">
		<input id="instantsearch__input" type="search" placeholder="search (ctrl+/)" tabindex="1">
		<button id="instantsearch__clear-button" type="reset">
			<div class="lds-ellipsis">
				<div></div>
				<div></div>
				<div></div>
				<div></div>
			</div>
			clear&nbsp;search&nbsp;(esc)
		</button>
		<i id="instantsearch__search-icon" class="fa fa-search"></i>
	</form>
	<div id="instantsearch__results" tabindex="2"></div>



	<script>
		const searchForm = document.querySelector('#instantsearch__form');
		const searchBoxInput = searchForm.querySelector('#instantsearch__input');
		{
			searchBoxInput.addEventListener('keyup', (e) => {
				onSearchBoxKeyUp();
			});
			document.addEventListener('keydown', (e) => {
				if (e.key === 'Escape') {
					searchBoxInput.value = '';
					searchBoxInput.focus();
					onSearchBoxKeyUp();
				} else if (e.ctrlKey && e.key === '/') {
					e.preventDefault();
					searchBoxInput.style.display = 'inline';
					searchBoxInput.focus();
				}
			});
			document.addEventListener('click', (e) => {
				if (e.target.id != searchForm.id && e.target.id != searchBoxInput.id) {
					searchBoxInput.value = '';
					onSearchBoxKeyUp();
				}
			});
		}

		// Determine whether results are present in results div.
		const resultsElement = document.querySelector('#instantsearch__results');
		{
			const resultsMutationObserver = new MutationObserver((mutations) => {
				const body = document.querySelector('body');
				const resultItem = resultsElement.querySelector('li');
				if (resultItem) { // Results exist.
					body.classList.add('is-frozen');
				} else { // No results.
					body.classList.remove('is-frozen');
				}
			});
			{
				resultsMutationObserver.observe(resultsElement, {
					'childList': true
				});
			}
		}


		function onSearchBoxKeyUp() {
			const queryValue = searchBoxInput.value.trim();

			function _highlightPortions(string, returnTruncated = true) {

				const distinctSearchWords = [...new Set(queryValue
					.split(/([^\s"]+|"[^"]*")+/g) // Split on non-word characters and double-quoted strings.
					.map(searchWord => searchWord.replace(/^"+|"+$/g, '')) // Remove double quotes at each end of "word".
					.filter(searchWord => searchWord.trim().length > 1) // Filter out white space "search words".
				)];

				if (returnTruncated) {
					const snippets = [];
					distinctSearchWords.forEach(searchWord => {
						if (searchWord.length > 1) {
							const regex = new RegExp("(\\S*.{0,10})?\\b(" + searchWord + ")\\b(.{0,10}\\S*)?", "ig"); // Match whole word for keyword, plus up to 10 characters before and after.
							string.replace(regex, function(match, $1, $2, $3) {
								snippets.push(`${($1 ? "…" + $1 : "")}<span class="hit-item__snippet-highlight">${$2}</span>${($3 ? $3 + "…" : "")}`);
							});
						}
					});
					return snippets.join('');
				} else {
					distinctSearchWords.forEach(searchWord => {
						if (searchWord.length > 1) {
							const regex = new RegExp(`\\b(${searchWord})\\b`, 'gi'); // Match whole word for keyword, in order to highlight.
							string = string.replace(regex, `<span style="color: red; background: yellow; font-weight: bold;">$1</span>`);
						}
					});
					return string;
				}
			}

			const ldsEllipsis = document.querySelector('.lds-ellipsis');
			async function _searchLsb(query) {
				ldsEllipsis.style.display = 'inline-block';
				const response = await fetch('/meilisearch', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						'q': `${query}`,
						'limit': 100000
					})
				});
				return response;
			}

			if (queryValue.length > 1) {
				_searchLsb(queryValue)
					.then(response => response.json())
					.then(json => {
						ldsEllipsis.style.display = 'none';
						let searchResultsHtml = `
							<style>
								.hit-item {
									display: flex;
									gap: 3em;
									text-decoration: none;
								}
								.hit-item:hover {
									background: #f0f0f0;
								}
								.hit-item__snippet-highlight {
									color: red;
									background: yellow;
									font-weight: bold;
								}
								.hit-item__link {
									display: flex;
									gap: 1em;
									text-decoration: none;
									width: 100%;
								}
								.hit-item__link:focus {
									border: 3px solid #000;
								}
								.hit-item__thumbnail {
									height: 90px;
									width: 70px;
									height: minmax(100%, 90px);
									width: minmax(100%, 70px);
									object-fit: cover;
								}
								.hit-item__main-content {
									display: flex;
									flex-direction: column;
									justify-content: center;
								}
								.hit-item__main-content__title {
									color: darkblue;
								}
								.hit-item__main-content__body {
									color: royalblue;
								}
								.hit-item__main-content__body--highlighted {
									color: #333;
								}
								.hit-item__main-content__metakeywords {
									color: grey;
									font-size: smaller;
								}
								.hit-item__main-content__metadescription {
									color: grey;
									font-size: smaller;
									font-style: italic;
								}
								.hit-item__main-content__hrefurl {
									font-size: smaller;
									color: #333;
									font-weight: bold;
									text-decoration: underline;
								}
							</style>
							<ul style="display: flex; flex-direction: column; gap: 1em;">
						`;
						json.hits
							.forEach(hit => {
								const highlightedTitle = _highlightPortions(hit.title, false);
								const highlightedBody = _highlightPortions(hit.body);
								const highlightedMetaKeywords = _highlightPortions(hit.metaKeywords);
								const highlightedMetaDescription = _highlightPortions(hit.metaDescription);
								searchResultsHtml += `
										<li class="hit-item">
											<a class="hit-item__link" href="${hit.hrefUrl}">
												<img loading="lazy" class="hit-item__thumbnail" src="${hit.thumbnailUrl}">
												<div class="hit-item__main-content">
													<h3 class="hit-item__main-content__title">${highlightedTitle}</h3>
													<p class="hit-item__main-content__body">${hit.body.substring(0, 100)}...<br><span class="hit-item__body--highlighted">${highlightedBody}</span></p>
													<p class="hit-item__main-content__metakeywords">${highlightedMetaKeywords}</p>
													<p class="hit-item__main-content__metadescription">${highlightedMetaDescription}</p>
													<p class="hit-item__main-content__hrefurl">${hit.hrefUrl}</p>
												</div>
											</a>
										</li>
									`;
							});
						searchResultsHtml += '</ul>';
						resultsElement.innerHTML = searchResultsHtml;
					});
			} else {
				resultsElement.innerHTML = '';
			}
		}
		onSearchBoxKeyUp();
	</script>
</div>