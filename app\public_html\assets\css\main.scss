@import url("https://fonts.googleapis.com/css2?family=Montserrat&family=Poppins:wght@400;500;600;700&display=swap");
@import url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css");

*,
*::before,
*::after {
	box-sizing: border-box;
}

:root {
	--clr-offwhite:  #f9fafb;
	// --clr-offwhite:  #fffafb;
	--clr-gold: #c1b291;
	--clr-linear-gold: linear-gradient(to right, #c1b291, #fff);
	--clr-linear-gold-reverse: linear-gradient(to left, #c1b291, #fff);
	--clr-linear-gold-to-bottom: linear-gradient(to bottom, #c1b291, #fff);
	//--------------------------
	// --clr-radial-orange: radial-gradient(#faa700, #e48d00);
	// --clr-radial-khaki: radial-gradient(#e9d2a5, #e0bf7b);
	// --clr-radial-green: radial-gradient(#009b00, #008000);
	// --clr-radial-blue: radial-gradient(#6dd5ed, #2193b0);
	// --clr-radial-darkerblue: radial-gradient(#06a2c5, #006d88);
	// --clr-accent-gold: #fce4b3;
	//--------------------------
	--clr-accent-red: #cb1829;
	--clr-dark-blue: #00008b;
	--clr-info-blue: #00416b;
	--clr-sky-blue: #007ac5;
	--clr-success-green: #28a745;
	--hgt-header-logo-card: 2rem;
	--wdh-header-logo-card: 100%;
	--hght-footer: 6rem;
	--hgt-left-bar: calc(100vh - var(--hgt-header-logo-card) - var(--hght-footer));
	--wdh-left-bar: 0rem;
	--wdh-insco-cards-container: 50%;
	--wdh-category-link: 100%;
	--wdh-services-label: 10rem;
	--txtshdw-modal: 0.03em 0.03em 0.01em #000;



	@media screen and (min-width: 280px) {
		--hght-footer: 5rem;
	}

	@media screen and (min-width: 400px) {
		--hght-footer: 4.5rem;
	}

	@media screen and (min-width: 500px) {
		--hght-footer: 2.5rem;
	}
}

body {
	background-color: var(--clr-offwhite);
	font-family: "Poppins", sans-serif;
	margin: 0;

	.service-link,
	.category-link,
	.insco-card {
		pointer-events: auto;
	}

	&.is-frozen {
		overflow: hidden;
		pointer-events: none;
		user-select: none;

		.service-link,
		.category-link,
		.insco-card {
			pointer-events: none;
		}
	}
}

h1 {
	all: unset;
}

h1.category-modal__main-content__heading,
h1.service-modal__main-content__heading {
	display: block;
	font-size: 1.5rem;
	font-weight: bold;
	margin: 0.825em 0;
}

h1.information-request-form-shell__form-header,
h1.mike-reisel-conversation-request-form-shell__form-header,
h1.tnbc-case-consultation-request-form-shell__form-header {
	font-size: 1.18rem;
}

p {
	line-height: 1.75;
}

img {
	max-width: 100%;
	object-fit: contain;
}

video {
	width: 100%;
	max-width: 100%;
	// max-height: 240px;
	// max-width: 320px;
}

form {
	select {
		cursor: pointer;
	}
}

.hidden-nav {
	display: none;
}

.link-image-container {
	display: inline-block;

	img {

		&:hover,
		&:focus {
			box-shadow: 0 0 3px #000;
		}
	}
}

.collapsible-section {
	.collapsible-toggle {
		cursor: pointer;
		color: blue;
		text-decoration: underline;
	}
}

.find-out-more-button,
.contract-now-button,
.registration-button,
.case-consultation-button,
.upcoming-webinar-button {
	z-index: 1;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-left: 0.75em;
	height: 2.25em;
	width: 8em;
	border-radius: 0.2em;
	color: #fff !important;
	text-decoration: none;
	text-align: center;
	font-family: sans-serif;
	font-size: 1.2rem;
	background: var(--clr-dark-blue);
	cursor: pointer;
	transform-origin: bottom;

	&:hover,
	&:focus {
		transform: scale(1.01);
		box-shadow: 0 0 0.2em #fff;
	}
}

.contract-now-button,
.registration-button,
.case-consultation-button,
.upcoming-webinar-button {
	margin-top: 0.25em;
	margin-bottom: 0.25em;
}


.case-consultation-button,
.upcoming-webinar-button {
	background: var(--clr-dark-blue);
	width: 15em;
	height: 75px;

	&__ray-main-image,
	&__chris-weston-image,
	&__john-baumhover-image,
	&__leon-hiracheta-image,
	&__randy-rowray-image,
	&__brittney-shaw-image,
	&__tyler-brooks-image,
	&__josh-strause-image,
	&__ginger-burke-image,
	&__brian-thompson-image,
	&__aaron-peterson-image,
	&__nathan-akers-image,
	&__mike-reisel-image,
	&__gold-silver-webinar-image {
		height: 100%;
	}

	&__randy-rowray-image {
		margin: 0;
	}


	&__john-baumhover-image,
	&__mike-reisel-image {
		margin-right: 0.5em;
	}
}

.page-content {
	position: relative;
	min-height: 100vh;
	min-width: 320px;
	max-width: 1920px;
	margin: 0 auto;
	margin-bottom: var(--hght-footer); // Footer height.
}

.footer {
	display: flex;
	align-items: center;
	justify-content: center;
	flex-wrap: wrap;
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 10000;
	background-color: var(--clr-offwhite);
	height: var(--hght-footer);
	font-size: 0.8rem;
	padding: 0 2em;
	border-top: solid 0.1px #aaa;
	pointer-events: all;

	&__find-out-more-button {
		color: #000 !important;
		background: #fff;
		border: 1px solid var(--clr-gold);
		height: unset;
		width: 7.5em;
		font-size: 1rem;
		animation: find-out-more-bump-out 30s ease-in-out 5s;
		animation-iteration-count: infinite;
	}

	&__logo-image {
		display: none;
		height: 80%;

		img {
			height: 100%;
		}
	}
}

@keyframes find-out-more-bump-out {
	1% {
		transform: scale(1.2);
		background: var(--clr-dark-blue);
	}

	5% {
		transform: scale(1);
		background: #fff;
	}
}

.left-bar {
	position: absolute;
	z-index: 2;
	top: calc(var(--hgt-header-logo-card) - 1em);
	left: 0;
	height: calc(var(--hgt-left-bar) + 1em);
	width: var(--wdh-left-bar);
	background: var(--clr-linear-gold-to-bottom);
}

// .landing-page-topbar,
.services-navbar {
	position: absolute;
	z-index: 1;
	top: calc(var(--hgt-header-logo-card) + 1.9em);
	left: 0;
	height: 220px;
	display: flex;
	justify-content: center;
	align-items: center;
	background: var(--clr-sky-blue);

	.service-list {
		margin: -0.5em 0 0 0;
		padding: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-wrap: wrap;
		pointer-events: none;
		list-style: none;

		.service-link {
			height: 1rem;
			padding: 1em 1em;
			display: flex;
			align-items: center;
			color: #fff;
			font-size: 0.8rem;
			text-decoration: none;
			text-align: center;
			position: relative;
			transition: transform 0.1s ease-in;

			&:hover {
				text-decoration: underline;
				transform: scale(1.01);
			}
		}

		.service-link-group {
			display: flex;

			&--ce-industry-marketing-resources {
				margin-top: 0.7em;
				margin-left: 1em;
			}
		}
	}
}

// .landing-page-topbar {
// 	@media screen and (min-width: 335px) {
// 		height: 185px;
// 	}

// 	@media screen and (min-width: 350px) {
// 		height: 155px;
// 	}

// 	@media screen and (min-width: 430px) {
// 		top: 0;
// 		left: var(--wdh-header-logo-card);
// 		right: 0;
// 		height: var(--hgt-header-logo-card);
// 	}
// }

.header-logo-card {
	position: absolute;
	// top: 0;
	top: 1.9em;
	left: 0;
	font-family: "Montserrat", sans-serif;
	background: var(--clr-gold);
	display: flex;
	justify-content: center;
	align-items: center;
	height: var(--hgt-header-logo-card);
	width: var(--wdh-header-logo-card);

	&__text {
		text-align: center;
		margin: 0;

		text-transform: uppercase;
		font-size: 0.9rem;

		&__word {
			display: inline-block;

			&::first-letter {
				font-size: 1.6em;
			}

			&:not(::first-letter) {
				font-size: 1.13em;
			}
		}

		&--big-capitalize {
			font-size: 1.6em;
		}
	}
}

.insco-category-section {
	pointer-events: none;
	position: absolute;
	top: calc(250px + 1.9em);
	left: var(--wdh-left-bar);
	right: 0;

	.insco-cards-container {
		position: relative;
		z-index: 2;
		pointer-events: none;
		height: 225px;
		width: var(--wdh-insco-cards-container);
		padding: 0 0.5em;
		margin-top: 20px;

		.insco-card {
			display: flex;
			justify-content: center;
			height: calc(100% / 2.3);
			width: 100%;
			cursor: pointer;
			transition: transform 0.1s ease-in;

			&:hover {
				transform: scale(1.01);
			}
		}
	}

	.category-links-container {
		pointer-events: none;
		position: absolute;
		left: var(--wdh-insco-cards-container);
		display: flex;
		justify-content: center;
		flex-wrap: wrap;
		height: 90%;
		padding-bottom: 0.5em;
		margin: 0.5em 2%;

		.category-link {
			display: flex;
			justify-content: center;
			align-items: center;
			text-align: center;
			text-decoration: none;
			font-size: 0.7rem;
			color: #000;
			width: var(--wdh-category-link);
			transition: transform 0.1s ease-in;

			h3 {
				margin: unset;
			}

			&:hover {
				text-decoration: underline;
				transform: scale(1.01);
			}


			@media screen and (max-width: 475px) {
				margin-top: unset;
			}


			@media screen and (min-width: 1485px) {
				// font-size: unset;
				font-size: 0.85rem;
			}

			@media screen and (min-width: 1700px) {
				// margin: 0 5%;
				font-size: unset;
			}
		}

		@media screen and (min-width: 630px) {
			margin-top: unset;
		}

		// @media screen and (min-width: 1630px) {
		// 	margin: 0 5%;
		// }

		// @media screen and (min-width: 1700px) {
		// 	// margin: 0 5%;
		// }
	}
}

.carousel-aside-container {
	position: absolute;
	top: calc(475px + 1.9em);
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 1;
	min-height: 450px;
	padding-bottom: 40px;
}

//=================================
// Shine effect on hover.
.contract-now-button,
.registration-button,
.form-submit-button,
.find-out-more-button,
.modal-close-button,
.insco-card,
.insco-products-card,
.upcoming-webinar-button,
.case-consultation-button,
.lsb-network-production-bonuses-available-button,
.current-myga-rates-button,
.assured-edge-calculator-button,
// .apasi--lsb-network-production-bonuses-available__button,
.service-modal--training__mike-reisel-section__button {
	position: relative;
	overflow: hidden;

	&::after {
		content: "";
		position: absolute;
		top: 0;
		left: 0;
		width: 0%;
		height: 100%;
		background: rgba(255, 255, 255, 0.6);
		opacity: 1;
		transition: none;
	}

	&:hover::after {
		width: 120%;
		opacity: 0;
		background: rgba(255, 255, 255, 0);
		transform: translateX(120%);
		transition: all 0.3s ease-out;
	}
}

.footer__find-out-more-button {
	&::after {
		background: rgba(0, 0, 0, 0.6);
	}

	&:hover::after {
		background: rgba(0, 0, 0, 0);
	}
}

//=================================

.aside-content {
	height: 200px;

	img {
		width: 100%;
	}
}

//==========================================================
//For bigger screen sizes......
@media screen and (min-width: 335px) {
	.services-navbar {
		height: 185px;
	}

	.insco-category-section {
		top: calc(215px + 1.9em); //290px;
	}

	.carousel-aside-container {
		top: calc(445px + 1.9em); //520px;
	}
}

@media screen and (min-width: 350px) {
	.services-navbar {
		height: 155px;
	}

	.insco-category-section {
		top: calc(185px + 1.9em); //260px;
	}

	.carousel-aside-container {
		top: calc(415px + 1.9em); //490px;
	}
}

@media screen and (min-width: 430px) {
	:root {
		--hgt-header-logo-card: 14.5rem;
		--wdh-header-logo-card: 10rem;
	}

	.contact-info {
		left: unset;
	}

	.services-navbar {
		top: 0;
		left: var(--wdh-header-logo-card);
		right: 0;
		height: var(--hgt-header-logo-card);
		padding-top: 1em;

		.service-list {
			.service-link {
				padding: 1em 0.5em;
			}

			.service-link-group {
				&--ce-industry-marketing-resources {
					margin-top: 0.5em;
				}
			}
		}
	}

	.insco-category-section {
		top: 237px;
	}

	.carousel-aside-container {
		top: 465px;
	}

	.header-bar {
		display: flex;
	}

	.header-logo-card {
		top: 0;
		padding-top: unset;

		&__text {
			font-size: 1.3rem;
		}
	}
}

@media screen and (min-width: 475px) {
	:root {
		--hgt-header-logo-card: 13rem;
	}

	.insco-category-section {
		top: 210px;
	}

	.carousel-aside-container {
		top: 440px;
	}
}

@media screen and (min-width: 490px) {
	:root {
		--hgt-header-logo-card: 11.5rem;
	}

	.insco-category-section {
		top: 185px;
	}

	.carousel-aside-container {
		top: 411px;
	}
}

@media screen and (min-width: 547px) {
	:root {
		--hgt-header-logo-card: 11.8rem;
	}

	.services-navbar {
		.service-list {
			.service-link {
				padding: 1.15em 0.5em;
			}

			.service-link-group {
				&--ce-industry-marketing-resources {
					margin-top: unset;
				}
			}
		}
	}

	.insco-category-section {
		top: 190px;
	}

	.carousel-aside-container {
		top: 416px;
	}
}

@media screen and (min-width: 559px) {
	:root {
		--hgt-header-logo-card: 12rem;
	}

	.services-navbar {
		.service-list {
			.service-link {
				padding: 1.1em 1em;
			}

			.service-link-group {
				&--ce-industry-marketing-resources {
					margin-top: 0.65em;
				}
			}
		}
	}

	.insco-category-section {
		top: 193px;
	}

	.carousel-aside-container {
		top: 440px;
	}

	.aside-content {
		position: absolute;
		top: 220px;
	}
}

@media screen and (min-width: 598px) {
	:root {
		--hgt-header-logo-card: 9.25rem;
	}

	.services-navbar {
		.service-list {
			margin-top: unset;

			.service-link-group {
				&--ce-industry-marketing-resources {
					margin-top: unset;
				}
			}
		}
	}

	.insco-category-section {
		top: 150px;
	}

	.carousel-aside-container {
		top: 390px;
	}
}

//==========================================================
// Normal desktop layout.............
@media screen and (min-width: 630px) {

	:root {
		--wdh-category-link: calc(100% / 7);
		--wdh-insco-cards-container: 200px;
	}

	.insco-category-section {
		left: var(--wdh-left-bar);
		right: 0;
		height: 500px;

		.insco-cards-container {
			margin-top: 45px;
			padding-right: 25px;

			&>*+* {
				margin-top: 0.5em;
			}
		}

		.category-links-container {
			left: 0;
			right: 0;
			height: 50px;
			padding-top: unset;
		}
	}

	.carousel-aside-container {
		top: 175px;
		left: calc(var(--wdh-insco-cards-container) + var(--wdh-left-bar));
		right: 0;
	}

	.aside-content {
		top: 250px;
		left: calc(-1 * var(--wdh-insco-cards-container));
		right: 0;
	}
}

@media screen and (min-width: 702px) {
	.footer__logo-image {
		display: unset;
	}

	.services-navbar {
		.service-list {
			.service-link {
				padding: 1.1em 1em;
			}
		}
	}
}

@media screen and (min-width: 771px) {
	.services-navbar {
		.service-list {
			.service-link {
				height: 2.25rem;
			}
		}
	}

	.insco-category-section {
		.insco-cards-container {
			margin-top: 50px;
		}
	}

	.carousel-aside-container {
		top: 190px;
	}
}

@media screen and (min-width: 800px) {
	.insco-category-section {
		.insco-cards-container {
			margin-top: 60px;
		}
	}

	.carousel-aside-container {
		top: 200px;
	}
}

@media screen and (min-width: 820px) {
	:root {
		--hgt-header-logo-card: 9.375rem;
	}

	.insco-category-section {
		.category-links-container {
			height: 50px;
			padding-bottom: unset;
		}

		.insco-cards-container {
			margin-top: 65px;

			&>*+* {
				margin-top: 0.8em;
			}
		}
	}

	.aside-content {
		top: 275px;
	}
}

@media screen and (min-width: 950px) {
	:root {
		--wdh-insco-cards-container: 225px;
	}

	.insco-category-section {

		.insco-cards-container {
			margin-left: 25px;
		}
	}
}

@media screen and (min-width: 1020px) {
	.services-navbar {
		.service-list {
			.service-link {
				font-size: 0.9rem;
			}
		}
	}

	.insco-category-section {
		.insco-cards-container {
			margin-left: 50px;
		}
	}
}

@media screen and (min-width: 1120px) {
	.insco-category-section {
		.insco-cards-container {
			margin-left: 70px;
		}
	}
}

@media screen and (min-width: 1120px) {
	:root {
		--wdh-insco-cards-container: 300px;
	}

	.insco-category-section {
		height: 600px;
	}
}

@media screen and (min-width: 1220px) {
	.insco-category-section {
		.insco-cards-container {
			margin-left: 100px;
		}
	}
}

@media screen and (min-width: 1330px) {
	:root {
		--wdh-insco-cards-container: 350px;
	}

	.aside-content {
		top: 400px;
	}

	.insco-category-section {
		.insco-cards-container {
			margin-top: 75px;
			height: 350px;
			// margin-left: 30px;
			margin-left: 25px;
		}
	}
}

@media screen and (min-width: 1450px) {
	.insco-category-section {
		.insco-cards-container {
			margin-left: 20px;
		}
	}
}

@media screen and (min-width: 1485px) {
	.services-navbar {
		.service-list {
			.service-link {
				font-size: 1.1rem;
			}
		}
	}

	.insco-category-section {
		.insco-cards-container {
			margin-left: 30px;
		}
	}
}

@media screen and (min-width: 1620px) {
	.services-navbar {
		.service-list {
			.service-link {
				font-size: 1.2rem;
			}
		}
	}

	.insco-category-section {
		.insco-cards-container {
			margin-left: 75px;
		}
	}
}

@media screen and (min-width: 1720px) {
	.insco-category-section {
		.insco-cards-container {
			margin-left: 115px;
		}
	}
}

//==========================================================
@import "modals";
@import "carousels";
@import "body-fade-in-animation";
@import "floating-logos";