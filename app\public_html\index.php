<?php
//======to display php errors =============
// error_reporting(E_ALL);
// ini_set("display_errors", 1);
//=========================================

if (session_status() == PHP_SESSION_NONE) {
	session_start();
}

// Defined constants.----------------------
define('APP_ROOT', __DIR__ . '/../');
define('PUBLIC_ROOT', __DIR__);
define('BASE_URL', (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST']);

// Load composer libraries, .ENV file, and $config variable.-----------------
require_once(APP_ROOT . 'vendor/autoload.php'); {
	$dotenv = Dotenv\Dotenv::createImmutable(APP_ROOT);
	$dotenv->load();
	$dotenv->required('APP_ENV')->allowedValues(['dev', 'test', 'prod']);
}
$config = json_decode(json_encode(include(APP_ROOT . "config-{$_ENV['APP_ENV']}.php")));


function _sendPdfFileResponse($filename)
{
	header('Content-Type: application/pdf');
	header('Content-Disposition: filename="' . $filename . '"');
	$filepath = APP_ROOT . '/pdf/' . $filename;
	header('Content-Length: ' . filesize($filepath));
	readfile($filepath);
}

// Routes.----------------------------------
switch (strtolower(rtrim(@$_SERVER['REDIRECT_URL'] ?? '/', '/'))) {
	case '':
		require APP_ROOT . '/views/home.php';
		break;
	case '/test':
		require APP_ROOT . '/views/test.php';
		break;
	case '/hub':
		header('Location: https://app.hubspot.com/contacts/20928696/objects/0-1/views/my/list');
		break;

		// pdf files.--------------------------------
	case '/pdf/chasing-events-pricing':
		_sendPdfFileResponse('LifeStageBrokerage_chasing-events-pricing-20210511.pdf');
		break;
	case '/pdf/webinar-gold-silver':
		_sendPdfFileResponse('LifeStageBrokerage_webinar-gold-silver-20220203.pdf');
		break;
	case '/pdf/blp':
		_sendPdfFileResponse('LifeStageBrokerage_GenBridge_BLP_Product_Summary_M2164-BLP-0823.pdf');
		break;
	case '/pdf/current-myga-rates':
		require APP_ROOT . '/views/article-pages/modal--annuity-myga-rates.php';
		break;
		// require_once APP_ROOT . '/database/LsbConfigDB.php';
		// $url = LsbConfigDB\lkpKeyValueConfig::getMYGARatesPdfUrl();
		// header("Location: $url");
		// break;

		// case '/pdf/lsb-bonus-schedule-for-rep-level-advisors':
		// 	_sendPdfFileResponse('LifeStageBrokerage__Bonus_Schedule_for_Rep_Level_Advisors_revised20221227.pdf');
		// 	break;


		// Async service calls.----------------------
	case '/process-tnbc-new-agent-portal-request':
		if (@$_GET['async'] || @$_POST['async']) {
			require APP_ROOT . '/services/process-tnbc-new-agent-portal-request.php';
		}
		break;
	case '/process-apasi-new-agent-contract-request':
		if (@$_GET['async'] || @$_POST['async']) {
			require APP_ROOT . '/services/process-apasi-new-agent-contract-request.php';
		}
		break;
	case '/process-blp-new-agent-contract-request':
		if (@$_GET['async'] || @$_POST['async']) {
			require APP_ROOT . '/services/process-blp-new-agent-contract-request.php';
		}
		break;
	case '/process-mike-reisel-conversation-request':
		if (@$_GET['async'] || @$_POST['async']) {
			require APP_ROOT . '/services/process-mike-reisel-conversation-request.php';
		}
		break;
	case '/process-information-request':
		if (@$_GET['async'] || @$_POST['async']) {
			require APP_ROOT . '/services/process-information-request.php';
		}
		break;
	case '/process-find-out-more-request':
		if (@$_GET['async'] || @$_POST['async']) {
			require APP_ROOT . '/services/process-find-out-more-request.php';
		}
		break;
	case '/process-tnbc-case-consultation-request':
		if (@$_GET['async'] || @$_POST['async']) {
			require APP_ROOT . '/services/process-tnbc-case-consultation-request.php';
		}
		break;
	case '/process-life-case-consultation-request':
		if (@$_GET['async'] || @$_POST['async']) {
			require APP_ROOT . '/services/process-life-case-consultation-request.php';
		}
		break;
	case '/process-life-case-joint-consultation-request':
		if (@$_GET['async'] || @$_POST['async']) {
			require APP_ROOT . '/services/process-life-case-joint-consultation-request.php';
		}
		break;
	case '/process-annuity-case-consultation-request':
		if (@$_GET['async'] || @$_POST['async']) {
			require APP_ROOT . '/services/process-annuity-case-consultation-request.php';
		}
		break;
	case '/process-annuity-case-joint-consultation-request':
		if (@$_GET['async'] || @$_POST['async']) {
			require APP_ROOT . '/services/process-annuity-case-joint-consultation-request.php';
		}
		break;
	case '/process-disability-case-consultation-request':
		if (@$_GET['async'] || @$_POST['async']) {
			require APP_ROOT . '/services/process-disability-case-consultation-request.php';
		}
		break;
	case '/process-disability-case-joint-consultation-request':
		if (@$_GET['async'] || @$_POST['async']) {
			require APP_ROOT . '/services/process-disability-case-joint-consultation-request.php';
		}
		break;
	case '/process-ltc-case-consultation-request':
		if (@$_GET['async'] || @$_POST['async']) {
			require APP_ROOT . '/services/process-ltc-case-consultation-request.php';
		}
		break;
	case '/process-ltc-case-joint-consultation-request':
		if (@$_GET['async'] || @$_POST['async']) {
			require APP_ROOT . '/services/process-ltc-case-joint-consultation-request.php';
		}
		break;
	case '/process-medicare-case-consultation-request':
		if (@$_GET['async'] || @$_POST['async']) {
			require APP_ROOT . '/services/process-medicare-case-consultation-request.php';
		}
		break;
	case '/process-champ-health-case-consultation-request':
		if (@$_GET['async'] || @$_POST['async']) {
			require APP_ROOT . '/services/process-champ-health-case-consultation-request.php';
		}
		break;
	case '/process-group-benefits-business-consultation-request':
		if (@$_GET['async'] || @$_POST['async']) {
			require APP_ROOT . '/services/process-group-benefits-business-consultation-request.php';
		}
		break;
	case '/process-medmutualprotect-case-consultation-request':
		if (@$_GET['async'] || @$_POST['async']) {
			require APP_ROOT . '/services/process-medmutualprotect-case-consultation-request.php';
		}
		break;
	case '/process-gwic-case-consultation-request':
		if (@$_GET['async'] || @$_POST['async']) {
			require APP_ROOT . '/services/process-gwic-case-consultation-request.php';
		}
		break;
	case '/process-metals-case-consultation-request':
		if (@$_GET['async'] || @$_POST['async']) {
			require APP_ROOT . '/services/process-metals-case-consultation-request.php';
		}
		break;
	case '/process-life-stage-advisor-consultation-request':
		if (@$_GET['async'] || @$_POST['async']) {
			require APP_ROOT . '/services/process-life-stage-advisor-consultation-request.php';
		}
		break;

		// Articles.-----------------------------
	case '/articles/tnbc-the-national-benefit-corp':
		require APP_ROOT . '/views/article-pages/insco-modal--tnbc.php';
		break;
	case '/articles/apasi-american-pre-arrangement-services-inc':
	case '/career-apasi':
		require APP_ROOT . '/views/article-pages/insco-modal--apasi.php';
		break;
		// case '/articles/economic-update-calls-given':
		// 	require APP_ROOT . '/views/article-pages/service-modal--economic-update.php';
		// 	break;
	case '/articles/the-beneficiary-liquidity-plan':
	case '/blp':
		require APP_ROOT . '/views/article-pages/insco-modal--blp.php';
		break;
	case '/articles/insurance-product-update-calls-given':
		require APP_ROOT . '/views/article-pages/service-modal--insurance-product-update.php';
		break;
	case '/articles/staffing-resources-and-employment-opportunities':
		require APP_ROOT . '/views/article-pages/service-modal--staffing-resources.php';
		break;
	case '/articles/agencies-practices-for-sale':
		require APP_ROOT . '/views/article-pages/service-modal--agencies-for-sale.php';
		break;
	case '/articles/training-and-educational-opportunities':
		require APP_ROOT . '/views/article-pages/service-modal--training.php';
		break;
	case '/articles/client-webinars-offered':
		require APP_ROOT . '/views/article-pages/service-modal--client-webinars.php';
		break;
	case '/articles/live-events-planning-offered':
		require APP_ROOT . '/views/article-pages/service-modal--event-planning.php';
		break;
	case '/articles/executive-coaching-offered':
		require APP_ROOT . '/views/article-pages/service-modal--exec-coaching.php';
		break;
	case '/articles/continuing-education-resources':
		require APP_ROOT . '/views/article-pages/service-modal--ce-resources.php';
		break;
	case '/articles/industry-resources-for-financial-advisors':
		require APP_ROOT . '/views/article-pages/service-modal--industry-resources.php';
		break;
	case '/articles/marketing-resources-for-financial-advisors':
		require APP_ROOT . '/views/article-pages/service-modal--marketing-resources.php';
		break;
	case '/articles/chasing-events-live-event-planning-company':
		require APP_ROOT . '/views/article-pages/service-modal--chasing-events.php';
		break;
	case '/articles/fati-financial-advisor-training-institute':
		require APP_ROOT . '/views/article-pages/service-modal--fati.php';
		break;
	case '/articles/life-insurance-offered':
		require APP_ROOT . '/views/article-pages/category-modal--life.php';
		break;
	case '/articles/annuities-offered':
		require APP_ROOT . '/views/article-pages/category-modal--annuities.php';
		break;
	case '/articles/group-alternative-offerings':
		require APP_ROOT . '/views/article-pages/category-modal--alternatives.php';
		break;
	case '/articles/ltc-long-term-care-offered':
		require APP_ROOT . '/views/article-pages/category-modal--ltc.php';
		break;
	case '/articles/disability-income-offered':
		require APP_ROOT . '/views/article-pages/category-modal--disability.php';
		break;
	case '/articles/precious-metals-offered':
		require APP_ROOT . '/views/article-pages/category-modal--metals.php';
		break;
	case '/articles/medicare-supplements-offered':
		require APP_ROOT . '/views/article-pages/category-modal--medicare.php';
		break;
	case '/articles/champ-health-plan':
	case '/champ':
	case '/gb':
		require APP_ROOT . '/views/article-pages/category-modal--champ-health.php';
		break;
	case '/articles/champ-health-case-consultation-request':
		require APP_ROOT . '/views/article-pages/modal--champ-health-case-consultation-request.php';
		break;
	case '/articles/group-benefits-business-consultation-request':
	case '/gbc':
		require APP_ROOT . '/views/article-pages/modal--group-benefits-business-consultation-request.php';
		break;
	case '/articles/find-out-more-about-the-life-stage-brokerage-network':
	case '/find-out-more':
	case '/learn-more':
	case '/contractnow':
		require APP_ROOT . '/views/article-pages/modal--find-out-more.php';
		break;
	case '/articles/mike-reisel-author-financial-help-conversation-request-form':
		require APP_ROOT . '/views/article-pages/modal--mike-reisel-conversation-request.php';
		break;
	case '/articles/information-request-form-for-the-life-stage-brokerage-network':
		require APP_ROOT . '/views/article-pages/modal--information-request.php';
		break;
	case '/articles/tnbc-case-consultation-request-form':
		require APP_ROOT . '/views/article-pages/modal--tnbc-case-consultation-request.php';
		break;
	case '/articles/life-case-consultation-request':
		require APP_ROOT . '/views/article-pages/modal--life-case-consultation-request.php';
		break;
	case '/articles/life-case-joint-consultation-request':
		require APP_ROOT . '/views/article-pages/modal--life-case-joint-consultation-request.php';
		break;
	case '/articles/annuity-case-consultation-request':
		require APP_ROOT . '/views/article-pages/modal--annuity-case-consultation-request.php';
		break;
	case '/articles/annuity-case-joint-consultation-request':
		require APP_ROOT . '/views/article-pages/modal--annuity-case-joint-consultation-request.php';
		break;
	case '/articles/annuity-myga-rates':
		require APP_ROOT . '/views/article-pages/modal--annuity-myga-rates.php';
		break;
	case '/articles/disability-case-consultation-request':
		require APP_ROOT . '/views/article-pages/modal--disability-case-consultation-request.php';
		break;
	case '/articles/disability-case-joint-consultation-request':
		require APP_ROOT . '/views/article-pages/modal--disability-case-joint-consultation-request.php';
		break;
	case '/articles/ltc-case-consultation-request':
		require APP_ROOT . '/views/article-pages/modal--ltc-case-consultation-request.php';
		break;
	case '/articles/ltc-case-joint-consultation-request':
		require APP_ROOT . '/views/article-pages/modal--ltc-case-joint-consultation-request.php';
		break;
	case '/articles/medicare-case-consultation-request':
		require APP_ROOT . '/views/article-pages/modal--medicare-case-consultation-request.php';
		break;
	case '/articles/medmutualprotect-case-consultation-request':
		require APP_ROOT . '/views/article-pages/modal--medmutualprotect-case-consultation-request.php';
		break;
	case '/articles/gwic-case-consultation-request':
		require APP_ROOT . '/views/article-pages/modal--gwic-case-consultation-request.php';
		break;
	case '/articles/metals-case-consultation-request':
		require APP_ROOT . '/views/article-pages/modal--metals-case-consultation-request.php';
		break;

	case '/articles/webinar-gold-silver':
		require APP_ROOT . '/views/article-pages/webinar-modal--gold-silver.php';
		break;
	case '/articles/webinar-gold-silver-introduction':
		require APP_ROOT . '/views/article-pages/webinar-modal--gold-silver-introduction.php';
		break;

	case '/articles/ryans-story':
		require APP_ROOT . '/views/article-pages/story-modal--ryans-story.php';
		break;

		// Events.-----------------------------
	case '/events/2022-college-basketball-viewing-event':
		require APP_ROOT . '/views/landing-pages/landing-page--2022-college-basketball-viewing-event.php';
		break;
	case '/process-rsvp-2022-college-basketball-viewing-event':
		if (@$_GET['async'] || @$_POST['async']) {
			require APP_ROOT . '/services/process-rsvp-2022-college-basketball-viewing-event.php';
		}
		break;

	case '/ce/register':
	case '/fsp/register':
		header('Location: https://events.r20.constantcontact.com/register/eventReg?oeidk=a07ejc02mx508de4f3c&oseq=&c=&ch=');
		break;

	case '/fi':
		require APP_ROOT . '/views/article-pages/modal--fi.php';
		break;

	case '/advisor':
		require APP_ROOT . '/views/landing-pages/landing-page--advisor.php';
		break;

		// --------------------------------------
	default:
		// require APP_ROOT.'/views/404.php';
		header('Location: /');
		break;
}
