<?php
// meilisearch-proxy.php
// Proxy service to forward Meilisearch requests to the Meilisearch server

// Set appropriate headers for JSON response
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
	http_response_code(200);
	exit();
}

try {
	// Only allow POST requests for search
	if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
		http_response_code(405);
		echo json_encode(['error' => 'Method not allowed']);
		exit();
	}

	// Get the request body
	$input = file_get_contents('php://input');
	if (empty($input)) {
		http_response_code(400);
		echo json_encode(['error' => 'Request body is required']);
		exit();
	}

	// Validate JSON input
	$requestData = json_decode($input, true);
	if (json_last_error() !== JSON_ERROR_NONE) {
		http_response_code(400);
		echo json_encode(['error' => 'Invalid JSON in request body']);
		exit();
	}

	// Build the Meilisearch server URL
	// $meilisearchHost = $config->baseUrl . ':7701';
	$meilisearchHost = 'https://www.lifestagebrokerage.com:7701';
	$meilisearchUrl = $meilisearchHost . '/indexes/lsb/search';

	// Prepare headers for the Meilisearch request
	$headers = [
		'Content-Type: application/json',
		'Authorization: Bearer ' . $config->meilisearchApiKey
	];

	// Create stream context for file_get_contents
	$context = stream_context_create([
		'http' => [
			'method' => 'POST',
			'header' => implode("\r\n", $headers),
			'content' => $input,
			'timeout' => 30,
			'ignore_errors' => true // This allows us to get response even for HTTP error codes
		]
	]);

	// Execute the request using file_get_contents
	$response = file_get_contents($meilisearchUrl, false, $context);
	

	// Check if request failed
	if ($response === false) {
		$lastError = error_get_last();
		http_response_code(500);
		echo json_encode([
			'error' => 'Failed to connect to Meilisearch server',
			'details' => 'Network error or server unreachable',
			'url' => $meilisearchUrl,
			'last_error' => $lastError ? $lastError['message'] : 'Unknown error'
		]);
		exit();
	}

	// Get HTTP response code from headers
	$httpCode = 200; // Default
	if (isset($http_response_header)) {
		foreach ($http_response_header as $header) {
			if (preg_match('/^HTTP\/\d\.\d\s+(\d+)/', $header, $matches)) {
				$httpCode = (int)$matches[1];
				break;
			}
		}
	}

	// Validate that we got a valid JSON response
	$responseData = json_decode($response, true);
	if (json_last_error() !== JSON_ERROR_NONE) {
		http_response_code(500);
		echo json_encode([
			'error' => 'Invalid JSON response from Meilisearch server',
			'details' => json_last_error_msg(),
			'raw_response' => substr($response, 0, 500), // First 500 chars for debugging
			'http_code' => $httpCode
		]);
		exit();
	}

	// Forward the HTTP status code from Meilisearch
	http_response_code($httpCode);

	// Return the response from Meilisearch
	echo $response;
} catch (Exception $e) {
	// Handle any unexpected errors
	http_response_code(500);
	echo json_encode([
		'error' => 'Internal server error',
		'message' => $e->getMessage()
	]);
}
